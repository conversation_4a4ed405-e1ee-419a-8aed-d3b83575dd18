#!/usr/bin/env python3
"""
训练日志指标绘制脚本
解析MMAction2训练日志，绘制训练/验证指标和学习率曲线图

使用方法:
python plot_training_metrics.py /path/to/log/file.log
"""

import re
import os
import sys
import argparse
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import Dict, List, Tuple, Optional
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class TrainingLogParser:
    """训练日志解析器"""
    
    def __init__(self, log_path: str):
        self.log_path = log_path
        self.log_dir = os.path.dirname(log_path)
        
        # 存储解析的数据
        self.epochs = []
        self.train_acc = []
        self.train_loss = []
        self.val_acc = []
        self.val_loss = []
        self.learning_rates = []
        
        # 正则表达式模式
        self.train_pattern = re.compile(
            r'Epoch\(train\)\s+\[(\d+)\]\[\s*\d+/\d+\].*?'
            r'lr:\s*([\d\.e\-\+]+).*?'
            r'loss:\s*([\d\.]+).*?'
            r'top1_acc:\s*([\d\.]+)'
        )
        
        self.val_pattern = re.compile(
            r'Epoch\(val\)\s+\[(\d+)\]\[\s*\d+/\d+\].*?'
            r'acc/top1:\s*([\d\.]+)'
        )
    
    def parse_log(self) -> bool:
        """解析日志文件"""
        try:
            with open(self.log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析训练数据 - 只取每个epoch的最后一条记录
            train_matches = self.train_pattern.findall(content)
            val_matches = self.val_pattern.findall(content)
            
            # 处理训练数据 - 按epoch分组，取每个epoch的最后一条
            train_data_by_epoch = {}
            for match in train_matches:
                epoch, lr, loss, acc = match
                epoch = int(epoch)
                train_data_by_epoch[epoch] = {
                    'lr': float(lr),
                    'loss': float(loss),
                    'acc': float(acc)
                }
            
            # 处理验证数据
            val_data_by_epoch = {}
            for match in val_matches:
                epoch, acc = match
                epoch = int(epoch)
                val_data_by_epoch[epoch] = float(acc)
            
            # 合并数据
            all_epochs = sorted(set(train_data_by_epoch.keys()) | set(val_data_by_epoch.keys()))
            
            for epoch in all_epochs:
                self.epochs.append(epoch)
                
                # 训练数据
                if epoch in train_data_by_epoch:
                    train_data = train_data_by_epoch[epoch]
                    self.train_acc.append(train_data['acc'])
                    self.train_loss.append(train_data['loss'])
                    self.learning_rates.append(train_data['lr'])
                else:
                    # 如果某个epoch没有训练数据，用前一个值填充
                    self.train_acc.append(self.train_acc[-1] if self.train_acc else 0)
                    self.train_loss.append(self.train_loss[-1] if self.train_loss else 0)
                    self.learning_rates.append(self.learning_rates[-1] if self.learning_rates else 0)
                
                # 验证数据
                if epoch in val_data_by_epoch:
                    self.val_acc.append(val_data_by_epoch[epoch])
                    # 验证损失通常不在日志中，设为None
                    self.val_loss.append(None)
                else:
                    self.val_acc.append(None)
                    self.val_loss.append(None)
            
            print(f"成功解析日志文件: {self.log_path}")
            print(f"解析到 {len(self.epochs)} 个epoch的数据")
            return True
            
        except Exception as e:
            print(f"解析日志文件失败: {e}")
            return False
    
    def plot_metrics(self) -> None:
        """绘制训练指标图"""
        if not self.epochs:
            print("没有数据可绘制")
            return
        
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'训练指标 - {os.path.basename(self.log_path)}', fontsize=16, fontweight='bold')
        
        epochs = np.array(self.epochs)
        
        # 1. 准确率曲线
        ax1.plot(epochs, self.train_acc, 'b-', label='训练准确率', linewidth=2, marker='o', markersize=3)
        
        # 绘制验证准确率（跳过None值）
        val_epochs = []
        val_accs = []
        for i, acc in enumerate(self.val_acc):
            if acc is not None:
                val_epochs.append(epochs[i])
                val_accs.append(acc)
        
        if val_epochs:
            ax1.plot(val_epochs, val_accs, 'r-', label='验证准确率', linewidth=2, marker='s', markersize=3)
        
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('准确率')
        ax1.set_title('准确率变化曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1.05)
        
        # 2. 损失曲线
        ax2.plot(epochs, self.train_loss, 'b-', label='训练损失', linewidth=2, marker='o', markersize=3)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('损失值')
        ax2.set_title('训练损失变化曲线')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')  # 使用对数坐标
        
        # 3. 学习率曲线
        ax3.plot(epochs, self.learning_rates, 'g-', label='学习率', linewidth=2, marker='^', markersize=3)
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('学习率')
        ax3.set_title('学习率变化曲线')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')  # 使用对数坐标
        
        # 4. 训练-验证准确率对比（放大视图）
        if val_epochs:
            ax4.plot(epochs, self.train_acc, 'b-', label='训练准确率', linewidth=2, alpha=0.7)
            ax4.plot(val_epochs, val_accs, 'r-', label='验证准确率', linewidth=2, alpha=0.7)
            
            # 计算准确率范围，设置合适的y轴范围
            all_accs = self.train_acc + val_accs
            min_acc = min(all_accs)
            max_acc = max(all_accs)
            margin = (max_acc - min_acc) * 0.1
            ax4.set_ylim(max(0, min_acc - margin), min(1, max_acc + margin))
            
            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('准确率')
            ax4.set_title('准确率对比（放大视图）')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, '无验证数据', ha='center', va='center', transform=ax4.transAxes, fontsize=14)
            ax4.set_title('验证数据缺失')
        
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.log_dir, 'training_metrics.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练指标图已保存至: {save_path}")
        
        plt.show()
    
    def plot_learning_rate(self) -> None:
        """单独绘制学习率曲线图"""
        if not self.epochs:
            print("没有数据可绘制")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))
        
        epochs = np.array(self.epochs)
        ax.plot(epochs, self.learning_rates, 'g-', label='学习率', linewidth=2, marker='o', markersize=4)
        
        ax.set_xlabel('Epoch', fontsize=12)
        ax.set_ylabel('学习率', fontsize=12)
        ax.set_title(f'学习率变化曲线 - {os.path.basename(self.log_path)}', fontsize=14, fontweight='bold')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')
        
        # 添加数值标注（每10个epoch标注一次）
        for i in range(0, len(epochs), max(1, len(epochs)//10)):
            ax.annotate(f'{self.learning_rates[i]:.2e}', 
                       (epochs[i], self.learning_rates[i]),
                       textcoords="offset points", 
                       xytext=(0,10), 
                       ha='center', fontsize=8, alpha=0.7)
        
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.log_dir, 'learning_rate_curve.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"学习率曲线图已保存至: {save_path}")
        
        plt.show()
    
    def print_summary(self) -> None:
        """打印训练摘要"""
        if not self.epochs:
            return
        
        print("\n" + "="*50)
        print("训练摘要")
        print("="*50)
        print(f"总训练轮数: {max(self.epochs)}")
        print(f"最终训练准确率: {self.train_acc[-1]:.4f}")
        print(f"最终训练损失: {self.train_loss[-1]:.6f}")
        print(f"最终学习率: {self.learning_rates[-1]:.2e}")
        
        # 验证集最佳结果
        val_accs_clean = [acc for acc in self.val_acc if acc is not None]
        if val_accs_clean:
            best_val_acc = max(val_accs_clean)
            best_val_epoch = None
            for i, acc in enumerate(self.val_acc):
                if acc == best_val_acc:
                    best_val_epoch = self.epochs[i]
                    break
            print(f"最佳验证准确率: {best_val_acc:.4f} (Epoch {best_val_epoch})")
            print(f"最终验证准确率: {val_accs_clean[-1]:.4f}")


def main():
    parser = argparse.ArgumentParser(description='绘制MMAction2训练日志指标曲线')
    parser.add_argument('log_path', help='训练日志文件路径')
    parser.add_argument('--lr-only', action='store_true', help='只绘制学习率曲线')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.log_path):
        print(f"错误: 日志文件不存在: {args.log_path}")
        sys.exit(1)
    
    # 解析日志
    parser = TrainingLogParser(args.log_path)
    if not parser.parse_log():
        sys.exit(1)
    
    # 打印摘要
    parser.print_summary()
    
    # 绘制图表
    if args.lr_only:
        parser.plot_learning_rate()
    else:
        parser.plot_metrics()
        parser.plot_learning_rate()


if __name__ == "__main__":
    main()
