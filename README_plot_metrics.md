# 训练日志可视化脚本使用说明

## 功能介绍

`plot_training_metrics.py` 是一个用于解析和可视化 MMAction2 训练日志的 Python 脚本。它可以：

1. **解析训练日志**：自动提取训练准确率、验证准确率、训练损失和学习率数据
2. **生成综合指标图**：包含4个子图的训练指标总览
3. **生成学习率曲线图**：单独的学习率变化曲线
4. **输出训练摘要**：关键指标的文字总结

## 使用方法

### 基本用法

```bash
# 生成完整的训练指标图和学习率曲线图
python plot_training_metrics.py /path/to/log/file.log

# 只生成学习率曲线图
python plot_training_metrics.py /path/to/log/file.log --lr-only
```

### 实际示例

```bash
# 使用实验4的日志
python plot_training_metrics.py tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log

# 只绘制学习率曲线
python plot_training_metrics.py tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log --lr-only
```

## 输出文件

脚本会在日志文件所在目录生成以下文件：

1. **training_metrics.png**：包含4个子图的综合训练指标图
   - 左上：训练准确率 vs 验证准确率
   - 右上：训练损失曲线（对数坐标）
   - 左下：学习率变化曲线（对数坐标）
   - 右下：准确率对比放大视图

2. **learning_rate_curve.png**：单独的学习率变化曲线图

## 输出示例

### 控制台输出
```
找到 200 条训练记录
找到 100 条验证记录
成功解析日志文件: tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log
解析到 100 个epoch的数据

==================================================
Training Summary
==================================================
Total epochs: 100
Final training accuracy: 0.9815
Final training loss: 0.005000
Final learning rate: 1.27e-06
Best validation accuracy: 0.9225 (Epoch 48)
Final validation accuracy: 0.9181
Training metrics plot saved to: tools/work_dirs/pose_rgb_fusion/20250806_045654/training_metrics.png
Learning rate curve saved to: tools/work_dirs/pose_rgb_fusion/20250806_045654/learning_rate_curve.png
```

## 支持的日志格式

脚本支持标准的 MMAction2 训练日志格式，能够识别以下模式：

### 训练记录
```
Epoch(train) [1][100/200] lr: 0.001000, loss: 1.234567, top1_acc: 0.8500
```

### 验证记录
```
Epoch(val) [1][50/100] acc/top1: 0.8200
```

## 技术特性

- **自动解析**：使用正则表达式自动提取关键指标
- **数据清洗**：每个epoch只保留最后一条训练记录，避免重复
- **智能填充**：对缺失的数据点进行合理填充
- **高质量输出**：300 DPI 高分辨率图片输出
- **无界面运行**：使用 Agg 后端，支持服务器环境运行

## 依赖要求

```python
matplotlib
numpy
re (内置)
os (内置)
sys (内置)
argparse (内置)
```

## 安装依赖

```bash
pip install matplotlib numpy
```

## 故障排除

### 1. 找不到训练记录
- 检查日志文件路径是否正确
- 确认日志文件格式符合 MMAction2 标准

### 2. 中文字体问题
- 脚本已配置使用英文标签，避免字体问题
- 如需中文显示，请安装相应中文字体

### 3. 图片无法保存
- 检查日志目录是否有写入权限
- 确认磁盘空间充足

## 自定义修改

### 修改图片尺寸
```python
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))  # 修改 figsize
```

### 修改输出分辨率
```python
plt.savefig(save_path, dpi=300, bbox_inches='tight')  # 修改 dpi 值
```

### 添加新的指标
在 `parse_log()` 方法中添加新的正则表达式模式，并在绘图方法中添加相应的绘制代码。

## 版本信息

- **版本**：1.0
- **兼容性**：MMAction2 训练日志
- **Python版本**：3.6+
- **测试环境**：Ubuntu 20.04, Python 3.8

## 更新日志

### v1.0 (2025-01-09)
- 初始版本发布
- 支持训练准确率、验证准确率、训练损失、学习率的可视化
- 支持训练摘要输出
- 支持高分辨率图片输出
- 支持服务器环境无界面运行
