2025/08/08 11:29:59 - mmengine - INFO - ============================================================
2025/08/08 11:29:59 - mmengine - INFO - 🚀 开始训练多模态识别模型 - Focal Loss优化版本
2025/08/08 11:29:59 - mmengine - INFO - 📁 配置文件: ./configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py
2025/08/08 11:29:59 - mmengine - INFO - 📂 工作目录: ./work_dirs/PoseRgb_focal
2025/08/08 11:29:59 - mmengine - INFO - 🎯 损失函数类型: FocalLossWithSmoothing
2025/08/08 11:29:59 - mmengine - INFO -    ├─ Alpha权重: auto
2025/08/08 11:29:59 - mmengine - INFO -    ├─ Gamma参数: 2.0
2025/08/08 11:29:59 - mmengine - INFO -    └─ Label Smoothing: 0.1
2025/08/08 11:29:59 - mmengine - INFO - 🔧 自定义Hook数量: 2
2025/08/08 11:29:59 - mmengine - INFO -    ├─ Hook 1: ClassWeightHook (权重更新间隔: 10轮)
2025/08/08 11:29:59 - mmengine - INFO -    ├─ Hook 2: HardSampleHook (困难样本阈值: 0.7)
2025/08/08 11:29:59 - mmengine - INFO - 📊 训练轮数: 100
2025/08/08 11:29:59 - mmengine - INFO - 📦 批次大小: 128
2025/08/08 11:29:59 - mmengine - INFO - ============================================================
2025/08/08 11:30:09 - mmengine - INFO - 🏃 开始训练...
2025/08/08 14:08:45 - mmengine - INFO - 🎉 训练完成！
2025/08/08 14:08:45 - mmengine - INFO - 💾 模型和日志保存在: ./work_dirs/PoseRgb_focal
