2025/08/08 11:30:02 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.8.17 (default, Jul  5 2023, 21:04:15) [GCC 11.2.0]
    CUDA available: True
    numpy_random_seed: 347132662
    GPU 0,1,2,3: Tesla V100-SXM2-32GB
    CUDA_HOME: None
    GCC: gcc (Ubuntu 11.3.0-1ubuntu1~22.04) 11.3.0
    PyTorch: 1.13.1+cu117
    PyTorch compiling details: PyTorch built with:
  - GCC 9.3
  - C++ Version: 201402
  - Intel(R) Math Kernel Library Version 2020.0.0 Product Build 20191122 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v2.6.0 (Git Hash 52b5f107dd9cf10910aaa19cb47f3abf9b349815)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 11.7
  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86
  - CuDNN 8.5
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.7, CUDNN_VERSION=8.5.0, CXX_COMPILER=/opt/rh/devtoolset-9/root/usr/bin/c++, CXX_FLAGS= -fabi-version=11 -Wno-deprecated -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -fopenmp -DNDEBUG -DUSE_KINETO -DUSE_FBGEMM -DUSE_QNNPACK -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -DEDGE_PROFILER_USE_KINETO -O2 -fPIC -Wno-narrowing -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Wno-missing-field-initializers -Wno-type-limits -Wno-array-bounds -Wno-unknown-pragmas -Wunused-local-typedefs -Wno-unused-parameter -Wno-unused-function -Wno-unused-result -Wno-strict-overflow -Wno-strict-aliasing -Wno-error=deprecated-declarations -Wno-stringop-overflow -Wno-psabi -Wno-error=pedantic -Wno-error=redundant-decls -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-unused-but-set-variable -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Werror=cast-function-type -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=1.13.1, USE_CUDA=ON, USE_CUDNN=ON, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=ON, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, 

    TorchVision: 0.14.1+cu117
    OpenCV: 4.11.0
    MMEngine: 0.8.4

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 347132662
    diff_rank_seed: False
    deterministic: False
    Distributed launcher: pytorch
    Distributed training: True
    GPU number: 4
------------------------------------------------------------

2025/08/08 11:30:04 - mmengine - INFO - Config:
Test_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl'
TrainVal_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl'
class_counts = [
    6258,
    5494,
    4751,
]
custom_hooks = [
    dict(
        adjustment_factor=0.1,
        max_weight=3.0,
        min_weight=0.5,
        target_metrics=dict(
            abnormal_detection_rate=0.97, normal_false_positive_rate=0.015),
        target_ratios=[
            1.0,
            1.2,
            1.1,
        ],
        type='ClassWeightHook',
        update_interval=10),
    dict(
        difficulty_threshold=0.7,
        hard_sample_weight=2.0,
        min_hard_samples=50,
        queue_size=1000,
        type='HardSampleHook',
        update_interval=5),
]
dataset_type = 'PoseRgbDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=20, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
find_unused_parameters = True
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
model = dict(
    cls_head=dict(
        in_channels=512,
        loss_cls=dict(
            alpha='auto',
            class_counts=[
                6258,
                5494,
                4751,
            ],
            gamma=2.0,
            label_smoothing=0.1,
            reduction='mean',
            type='FocalLossWithSmoothing'),
        num_classes=3,
        type='TwoFusionHead'),
    fusion_neck=dict(
        dropout=0.5,
        fusion_dim=512,
        fusion_type='attention',
        img_feat_dim=320,
        pose_feat_dim=256,
        type='MultiModalFusionNeck'),
    image_backbone=dict(
        frozen_stages=-1,
        init_cfg=dict(
            checkpoint=
            './tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth',
            type='Pretrained'),
        is_shift=True,
        norm_eval=False,
        num_segments=3,
        out_indices=(6, ),
        pretrained2d=False,
        shift_div=8,
        type='MobileNetV2TSM'),
    pose_backbone=dict(
        graph_cfg=dict(layout='coco', mode='stgcn_spatial'),
        in_channels=3,
        init_cfg=dict(
            checkpoint=
            './stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth',
            type='Pretrained'),
        type='STGCN'),
    test_cfg=dict(average_clips='prob'),
    train_cfg=None,
    type='MultiModalRecognizer')
num_classes = 3
optim_wrapper = dict(
    clip_grad=dict(max_norm=20, norm_type=2),
    optimizer=dict(lr=0.01, momentum=0.9, type='SGD', weight_decay=0.0001),
    paramwise_cfg=dict(
        custom_keys=dict(
            cls_head=dict(lr_mult=1.0),
            fusion_neck=dict(lr_mult=0.5),
            image_backbone=dict(lr_mult=0.1),
            pose_backbone=dict(lr_mult=1.0))),
    type='OptimWrapper')
param_scheduler = [
    dict(begin=0, by_epoch=True, end=5, start_factor=0.1, type='LinearLR'),
    dict(T_max=95, begin=5, by_epoch=True, end=100, type='CosineAnnealingLR'),
]
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)
resume = False
split_label = dict({
    0: [
        1,
        2,
    ],
    1: [
        0,
    ]
})
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=64,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(input_format='NCHW', type='FormatShape'),
            dict(
                collect_keys=(
                    'keypoint',
                    'imgs',
                ),
                pred_txt=
                '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets',
                split_label=dict({
                    0: [
                        1,
                        2,
                    ],
                    1: [
                        0,
                    ]
                }),
                type='PackActionInputs_Test'),
        ],
        split=None,
        test_mode=True,
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = [
    dict(type='ClassifyReport'),
]
test_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(input_format='NCHW', type='FormatShape'),
    dict(
        collect_keys=(
            'keypoint',
            'imgs',
        ),
        pred_txt=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets',
        split_label=dict({
            0: [
                1,
                2,
            ],
            1: [
                0,
            ]
        }),
        type='PackActionInputs_Test'),
]
train_cfg = dict(
    max_epochs=100, type='EpochBasedTrainLoop', val_begin=1, val_interval=1)
train_dataloader = dict(
    batch_size=128,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, seed=255, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                load_Resized_img=True,
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(type='ColorJitter'),
            dict(flip_ratio=0.5, type='Flip'),
            dict(input_format='NCHW', type='FormatShape'),
            dict(collect_keys=(
                'keypoint',
                'imgs',
            ), type='PackActionInputs'),
        ],
        split='train',
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, seed=255, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        load_Resized_img=True,
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(type='ColorJitter'),
    dict(flip_ratio=0.5, type='Flip'),
    dict(input_format='NCHW', type='FormatShape'),
    dict(collect_keys=(
        'keypoint',
        'imgs',
    ), type='PackActionInputs'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=64,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                load_Resized_img=True,
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(input_format='NCHW', type='FormatShape'),
            dict(collect_keys=(
                'keypoint',
                'imgs',
            ), type='PackActionInputs'),
        ],
        split='val',
        test_mode=True,
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = [
    dict(type='AccMetric'),
]
val_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        load_Resized_img=True,
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(input_format='NCHW', type='FormatShape'),
    dict(collect_keys=(
        'keypoint',
        'imgs',
    ), type='PackActionInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    type='ActionVisualizer', vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = './work_dirs/PoseRgb_focal'

2025/08/08 11:30:07 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) ClassWeightHook                    
(NORMAL      ) HardSampleHook                     
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) HardSampleHook                     
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SyncBuffersHook                    
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SyncBuffersHook                    
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) ClassWeightHook                    
(NORMAL      ) HardSampleHook                     
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/08/08 11:30:10 - mmengine - INFO - 14038 videos remain after valid thresholding
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.PA:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.PA:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.PA:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.conv.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.conv.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.conv.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.weight:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.weight:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.bias:lr=0.001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.bias:lr_mult=0.1
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.weight:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.weight:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.bias:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.bias:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.weight:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.weight:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.bias:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.bias:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_weight:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_weight:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_bias:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_bias:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.weight:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.weight:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.bias:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.bias:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.weight:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.weight:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.bias:lr=0.005
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.bias:lr_mult=0.5
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.bias:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.weight:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.weight:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.weight:lr_mult=1.0
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.bias:lr=0.01
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.bias:weight_decay=0.0001
2025/08/08 11:31:24 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.bias:lr_mult=1.0
2025/08/08 11:31:26 - mmengine - INFO - 2465 videos remain after valid thresholding
2025/08/08 11:31:28 - mmengine - INFO - load model from: ./tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth
2025/08/08 11:31:28 - mmengine - INFO - Loads checkpoint by local backend from path: ./tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth
2025/08/08 11:31:29 - mmengine - WARNING - The model and loaded state dict do not match exactly

unexpected key in source state_dict: backbone.conv1.conv.weight, backbone.conv1.bn.weight, backbone.conv1.bn.bias, backbone.conv1.bn.running_mean, backbone.conv1.bn.running_var, backbone.conv1.bn.num_batches_tracked, backbone.layer1.0.conv.0.conv.weight, backbone.layer1.0.conv.0.bn.weight, backbone.layer1.0.conv.0.bn.bias, backbone.layer1.0.conv.0.bn.running_mean, backbone.layer1.0.conv.0.bn.running_var, backbone.layer1.0.conv.0.bn.num_batches_tracked, backbone.layer1.0.conv.1.conv.weight, backbone.layer1.0.conv.1.bn.weight, backbone.layer1.0.conv.1.bn.bias, backbone.layer1.0.conv.1.bn.running_mean, backbone.layer1.0.conv.1.bn.running_var, backbone.layer1.0.conv.1.bn.num_batches_tracked, backbone.layer2.0.conv.0.conv.weight, backbone.layer2.0.conv.0.bn.weight, backbone.layer2.0.conv.0.bn.bias, backbone.layer2.0.conv.0.bn.running_mean, backbone.layer2.0.conv.0.bn.running_var, backbone.layer2.0.conv.0.bn.num_batches_tracked, backbone.layer2.0.conv.1.conv.weight, backbone.layer2.0.conv.1.bn.weight, backbone.layer2.0.conv.1.bn.bias, backbone.layer2.0.conv.1.bn.running_mean, backbone.layer2.0.conv.1.bn.running_var, backbone.layer2.0.conv.1.bn.num_batches_tracked, backbone.layer2.0.conv.2.conv.weight, backbone.layer2.0.conv.2.bn.weight, backbone.layer2.0.conv.2.bn.bias, backbone.layer2.0.conv.2.bn.running_mean, backbone.layer2.0.conv.2.bn.running_var, backbone.layer2.0.conv.2.bn.num_batches_tracked, backbone.layer2.1.conv.0.net.conv.weight, backbone.layer2.1.conv.0.net.bn.weight, backbone.layer2.1.conv.0.net.bn.bias, backbone.layer2.1.conv.0.net.bn.running_mean, backbone.layer2.1.conv.0.net.bn.running_var, backbone.layer2.1.conv.0.net.bn.num_batches_tracked, backbone.layer2.1.conv.1.conv.weight, backbone.layer2.1.conv.1.bn.weight, backbone.layer2.1.conv.1.bn.bias, backbone.layer2.1.conv.1.bn.running_mean, backbone.layer2.1.conv.1.bn.running_var, backbone.layer2.1.conv.1.bn.num_batches_tracked, backbone.layer2.1.conv.2.conv.weight, backbone.layer2.1.conv.2.bn.weight, backbone.layer2.1.conv.2.bn.bias, backbone.layer2.1.conv.2.bn.running_mean, backbone.layer2.1.conv.2.bn.running_var, backbone.layer2.1.conv.2.bn.num_batches_tracked, backbone.layer3.0.conv.0.conv.weight, backbone.layer3.0.conv.0.bn.weight, backbone.layer3.0.conv.0.bn.bias, backbone.layer3.0.conv.0.bn.running_mean, backbone.layer3.0.conv.0.bn.running_var, backbone.layer3.0.conv.0.bn.num_batches_tracked, backbone.layer3.0.conv.1.conv.weight, backbone.layer3.0.conv.1.bn.weight, backbone.layer3.0.conv.1.bn.bias, backbone.layer3.0.conv.1.bn.running_mean, backbone.layer3.0.conv.1.bn.running_var, backbone.layer3.0.conv.1.bn.num_batches_tracked, backbone.layer3.0.conv.2.conv.weight, backbone.layer3.0.conv.2.bn.weight, backbone.layer3.0.conv.2.bn.bias, backbone.layer3.0.conv.2.bn.running_mean, backbone.layer3.0.conv.2.bn.running_var, backbone.layer3.0.conv.2.bn.num_batches_tracked, backbone.layer3.1.conv.0.net.conv.weight, backbone.layer3.1.conv.0.net.bn.weight, backbone.layer3.1.conv.0.net.bn.bias, backbone.layer3.1.conv.0.net.bn.running_mean, backbone.layer3.1.conv.0.net.bn.running_var, backbone.layer3.1.conv.0.net.bn.num_batches_tracked, backbone.layer3.1.conv.1.conv.weight, backbone.layer3.1.conv.1.bn.weight, backbone.layer3.1.conv.1.bn.bias, backbone.layer3.1.conv.1.bn.running_mean, backbone.layer3.1.conv.1.bn.running_var, backbone.layer3.1.conv.1.bn.num_batches_tracked, backbone.layer3.1.conv.2.conv.weight, backbone.layer3.1.conv.2.bn.weight, backbone.layer3.1.conv.2.bn.bias, backbone.layer3.1.conv.2.bn.running_mean, backbone.layer3.1.conv.2.bn.running_var, backbone.layer3.1.conv.2.bn.num_batches_tracked, backbone.layer3.2.conv.0.net.conv.weight, backbone.layer3.2.conv.0.net.bn.weight, backbone.layer3.2.conv.0.net.bn.bias, backbone.layer3.2.conv.0.net.bn.running_mean, backbone.layer3.2.conv.0.net.bn.running_var, backbone.layer3.2.conv.0.net.bn.num_batches_tracked, backbone.layer3.2.conv.1.conv.weight, backbone.layer3.2.conv.1.bn.weight, backbone.layer3.2.conv.1.bn.bias, backbone.layer3.2.conv.1.bn.running_mean, backbone.layer3.2.conv.1.bn.running_var, backbone.layer3.2.conv.1.bn.num_batches_tracked, backbone.layer3.2.conv.2.conv.weight, backbone.layer3.2.conv.2.bn.weight, backbone.layer3.2.conv.2.bn.bias, backbone.layer3.2.conv.2.bn.running_mean, backbone.layer3.2.conv.2.bn.running_var, backbone.layer3.2.conv.2.bn.num_batches_tracked, backbone.layer4.0.conv.0.conv.weight, backbone.layer4.0.conv.0.bn.weight, backbone.layer4.0.conv.0.bn.bias, backbone.layer4.0.conv.0.bn.running_mean, backbone.layer4.0.conv.0.bn.running_var, backbone.layer4.0.conv.0.bn.num_batches_tracked, backbone.layer4.0.conv.1.conv.weight, backbone.layer4.0.conv.1.bn.weight, backbone.layer4.0.conv.1.bn.bias, backbone.layer4.0.conv.1.bn.running_mean, backbone.layer4.0.conv.1.bn.running_var, backbone.layer4.0.conv.1.bn.num_batches_tracked, backbone.layer4.0.conv.2.conv.weight, backbone.layer4.0.conv.2.bn.weight, backbone.layer4.0.conv.2.bn.bias, backbone.layer4.0.conv.2.bn.running_mean, backbone.layer4.0.conv.2.bn.running_var, backbone.layer4.0.conv.2.bn.num_batches_tracked, backbone.layer4.1.conv.0.net.conv.weight, backbone.layer4.1.conv.0.net.bn.weight, backbone.layer4.1.conv.0.net.bn.bias, backbone.layer4.1.conv.0.net.bn.running_mean, backbone.layer4.1.conv.0.net.bn.running_var, backbone.layer4.1.conv.0.net.bn.num_batches_tracked, backbone.layer4.1.conv.1.conv.weight, backbone.layer4.1.conv.1.bn.weight, backbone.layer4.1.conv.1.bn.bias, backbone.layer4.1.conv.1.bn.running_mean, backbone.layer4.1.conv.1.bn.running_var, backbone.layer4.1.conv.1.bn.num_batches_tracked, backbone.layer4.1.conv.2.conv.weight, backbone.layer4.1.conv.2.bn.weight, backbone.layer4.1.conv.2.bn.bias, backbone.layer4.1.conv.2.bn.running_mean, backbone.layer4.1.conv.2.bn.running_var, backbone.layer4.1.conv.2.bn.num_batches_tracked, backbone.layer4.2.conv.0.net.conv.weight, backbone.layer4.2.conv.0.net.bn.weight, backbone.layer4.2.conv.0.net.bn.bias, backbone.layer4.2.conv.0.net.bn.running_mean, backbone.layer4.2.conv.0.net.bn.running_var, backbone.layer4.2.conv.0.net.bn.num_batches_tracked, backbone.layer4.2.conv.1.conv.weight, backbone.layer4.2.conv.1.bn.weight, backbone.layer4.2.conv.1.bn.bias, backbone.layer4.2.conv.1.bn.running_mean, backbone.layer4.2.conv.1.bn.running_var, backbone.layer4.2.conv.1.bn.num_batches_tracked, backbone.layer4.2.conv.2.conv.weight, backbone.layer4.2.conv.2.bn.weight, backbone.layer4.2.conv.2.bn.bias, backbone.layer4.2.conv.2.bn.running_mean, backbone.layer4.2.conv.2.bn.running_var, backbone.layer4.2.conv.2.bn.num_batches_tracked, backbone.layer4.3.conv.0.net.conv.weight, backbone.layer4.3.conv.0.net.bn.weight, backbone.layer4.3.conv.0.net.bn.bias, backbone.layer4.3.conv.0.net.bn.running_mean, backbone.layer4.3.conv.0.net.bn.running_var, backbone.layer4.3.conv.0.net.bn.num_batches_tracked, backbone.layer4.3.conv.1.conv.weight, backbone.layer4.3.conv.1.bn.weight, backbone.layer4.3.conv.1.bn.bias, backbone.layer4.3.conv.1.bn.running_mean, backbone.layer4.3.conv.1.bn.running_var, backbone.layer4.3.conv.1.bn.num_batches_tracked, backbone.layer4.3.conv.2.conv.weight, backbone.layer4.3.conv.2.bn.weight, backbone.layer4.3.conv.2.bn.bias, backbone.layer4.3.conv.2.bn.running_mean, backbone.layer4.3.conv.2.bn.running_var, backbone.layer4.3.conv.2.bn.num_batches_tracked, backbone.layer5.0.conv.0.conv.weight, backbone.layer5.0.conv.0.bn.weight, backbone.layer5.0.conv.0.bn.bias, backbone.layer5.0.conv.0.bn.running_mean, backbone.layer5.0.conv.0.bn.running_var, backbone.layer5.0.conv.0.bn.num_batches_tracked, backbone.layer5.0.conv.1.conv.weight, backbone.layer5.0.conv.1.bn.weight, backbone.layer5.0.conv.1.bn.bias, backbone.layer5.0.conv.1.bn.running_mean, backbone.layer5.0.conv.1.bn.running_var, backbone.layer5.0.conv.1.bn.num_batches_tracked, backbone.layer5.0.conv.2.conv.weight, backbone.layer5.0.conv.2.bn.weight, backbone.layer5.0.conv.2.bn.bias, backbone.layer5.0.conv.2.bn.running_mean, backbone.layer5.0.conv.2.bn.running_var, backbone.layer5.0.conv.2.bn.num_batches_tracked, backbone.layer5.1.conv.0.net.conv.weight, backbone.layer5.1.conv.0.net.bn.weight, backbone.layer5.1.conv.0.net.bn.bias, backbone.layer5.1.conv.0.net.bn.running_mean, backbone.layer5.1.conv.0.net.bn.running_var, backbone.layer5.1.conv.0.net.bn.num_batches_tracked, backbone.layer5.1.conv.1.conv.weight, backbone.layer5.1.conv.1.bn.weight, backbone.layer5.1.conv.1.bn.bias, backbone.layer5.1.conv.1.bn.running_mean, backbone.layer5.1.conv.1.bn.running_var, backbone.layer5.1.conv.1.bn.num_batches_tracked, backbone.layer5.1.conv.2.conv.weight, backbone.layer5.1.conv.2.bn.weight, backbone.layer5.1.conv.2.bn.bias, backbone.layer5.1.conv.2.bn.running_mean, backbone.layer5.1.conv.2.bn.running_var, backbone.layer5.1.conv.2.bn.num_batches_tracked, backbone.layer5.2.conv.0.net.conv.weight, backbone.layer5.2.conv.0.net.bn.weight, backbone.layer5.2.conv.0.net.bn.bias, backbone.layer5.2.conv.0.net.bn.running_mean, backbone.layer5.2.conv.0.net.bn.running_var, backbone.layer5.2.conv.0.net.bn.num_batches_tracked, backbone.layer5.2.conv.1.conv.weight, backbone.layer5.2.conv.1.bn.weight, backbone.layer5.2.conv.1.bn.bias, backbone.layer5.2.conv.1.bn.running_mean, backbone.layer5.2.conv.1.bn.running_var, backbone.layer5.2.conv.1.bn.num_batches_tracked, backbone.layer5.2.conv.2.conv.weight, backbone.layer5.2.conv.2.bn.weight, backbone.layer5.2.conv.2.bn.bias, backbone.layer5.2.conv.2.bn.running_mean, backbone.layer5.2.conv.2.bn.running_var, backbone.layer5.2.conv.2.bn.num_batches_tracked, backbone.layer6.0.conv.0.conv.weight, backbone.layer6.0.conv.0.bn.weight, backbone.layer6.0.conv.0.bn.bias, backbone.layer6.0.conv.0.bn.running_mean, backbone.layer6.0.conv.0.bn.running_var, backbone.layer6.0.conv.0.bn.num_batches_tracked, backbone.layer6.0.conv.1.conv.weight, backbone.layer6.0.conv.1.bn.weight, backbone.layer6.0.conv.1.bn.bias, backbone.layer6.0.conv.1.bn.running_mean, backbone.layer6.0.conv.1.bn.running_var, backbone.layer6.0.conv.1.bn.num_batches_tracked, backbone.layer6.0.conv.2.conv.weight, backbone.layer6.0.conv.2.bn.weight, backbone.layer6.0.conv.2.bn.bias, backbone.layer6.0.conv.2.bn.running_mean, backbone.layer6.0.conv.2.bn.running_var, backbone.layer6.0.conv.2.bn.num_batches_tracked, backbone.layer6.1.conv.0.net.conv.weight, backbone.layer6.1.conv.0.net.bn.weight, backbone.layer6.1.conv.0.net.bn.bias, backbone.layer6.1.conv.0.net.bn.running_mean, backbone.layer6.1.conv.0.net.bn.running_var, backbone.layer6.1.conv.0.net.bn.num_batches_tracked, backbone.layer6.1.conv.1.conv.weight, backbone.layer6.1.conv.1.bn.weight, backbone.layer6.1.conv.1.bn.bias, backbone.layer6.1.conv.1.bn.running_mean, backbone.layer6.1.conv.1.bn.running_var, backbone.layer6.1.conv.1.bn.num_batches_tracked, backbone.layer6.1.conv.2.conv.weight, backbone.layer6.1.conv.2.bn.weight, backbone.layer6.1.conv.2.bn.bias, backbone.layer6.1.conv.2.bn.running_mean, backbone.layer6.1.conv.2.bn.running_var, backbone.layer6.1.conv.2.bn.num_batches_tracked, backbone.layer6.2.conv.0.net.conv.weight, backbone.layer6.2.conv.0.net.bn.weight, backbone.layer6.2.conv.0.net.bn.bias, backbone.layer6.2.conv.0.net.bn.running_mean, backbone.layer6.2.conv.0.net.bn.running_var, backbone.layer6.2.conv.0.net.bn.num_batches_tracked, backbone.layer6.2.conv.1.conv.weight, backbone.layer6.2.conv.1.bn.weight, backbone.layer6.2.conv.1.bn.bias, backbone.layer6.2.conv.1.bn.running_mean, backbone.layer6.2.conv.1.bn.running_var, backbone.layer6.2.conv.1.bn.num_batches_tracked, backbone.layer6.2.conv.2.conv.weight, backbone.layer6.2.conv.2.bn.weight, backbone.layer6.2.conv.2.bn.bias, backbone.layer6.2.conv.2.bn.running_mean, backbone.layer6.2.conv.2.bn.running_var, backbone.layer6.2.conv.2.bn.num_batches_tracked, backbone.layer7.0.conv.0.conv.weight, backbone.layer7.0.conv.0.bn.weight, backbone.layer7.0.conv.0.bn.bias, backbone.layer7.0.conv.0.bn.running_mean, backbone.layer7.0.conv.0.bn.running_var, backbone.layer7.0.conv.0.bn.num_batches_tracked, backbone.layer7.0.conv.1.conv.weight, backbone.layer7.0.conv.1.bn.weight, backbone.layer7.0.conv.1.bn.bias, backbone.layer7.0.conv.1.bn.running_mean, backbone.layer7.0.conv.1.bn.running_var, backbone.layer7.0.conv.1.bn.num_batches_tracked, backbone.layer7.0.conv.2.conv.weight, backbone.layer7.0.conv.2.bn.weight, backbone.layer7.0.conv.2.bn.bias, backbone.layer7.0.conv.2.bn.running_mean, backbone.layer7.0.conv.2.bn.running_var, backbone.layer7.0.conv.2.bn.num_batches_tracked, backbone.conv2.conv.weight, backbone.conv2.bn.weight, backbone.conv2.bn.bias, backbone.conv2.bn.running_mean, backbone.conv2.bn.running_var, backbone.conv2.bn.num_batches_tracked, cls_head.fc_cls.weight, cls_head.fc_cls.bias

missing keys in source state_dict: conv1.conv.weight, conv1.bn.weight, conv1.bn.bias, conv1.bn.running_mean, conv1.bn.running_var, layer1.0.conv.0.conv.weight, layer1.0.conv.0.bn.weight, layer1.0.conv.0.bn.bias, layer1.0.conv.0.bn.running_mean, layer1.0.conv.0.bn.running_var, layer1.0.conv.1.conv.weight, layer1.0.conv.1.bn.weight, layer1.0.conv.1.bn.bias, layer1.0.conv.1.bn.running_mean, layer1.0.conv.1.bn.running_var, layer2.0.conv.0.conv.weight, layer2.0.conv.0.bn.weight, layer2.0.conv.0.bn.bias, layer2.0.conv.0.bn.running_mean, layer2.0.conv.0.bn.running_var, layer2.0.conv.1.conv.weight, layer2.0.conv.1.bn.weight, layer2.0.conv.1.bn.bias, layer2.0.conv.1.bn.running_mean, layer2.0.conv.1.bn.running_var, layer2.0.conv.2.conv.weight, layer2.0.conv.2.bn.weight, layer2.0.conv.2.bn.bias, layer2.0.conv.2.bn.running_mean, layer2.0.conv.2.bn.running_var, layer2.1.conv.0.net.conv.weight, layer2.1.conv.0.net.bn.weight, layer2.1.conv.0.net.bn.bias, layer2.1.conv.0.net.bn.running_mean, layer2.1.conv.0.net.bn.running_var, layer2.1.conv.1.conv.weight, layer2.1.conv.1.bn.weight, layer2.1.conv.1.bn.bias, layer2.1.conv.1.bn.running_mean, layer2.1.conv.1.bn.running_var, layer2.1.conv.2.conv.weight, layer2.1.conv.2.bn.weight, layer2.1.conv.2.bn.bias, layer2.1.conv.2.bn.running_mean, layer2.1.conv.2.bn.running_var, layer3.0.conv.0.conv.weight, layer3.0.conv.0.bn.weight, layer3.0.conv.0.bn.bias, layer3.0.conv.0.bn.running_mean, layer3.0.conv.0.bn.running_var, layer3.0.conv.1.conv.weight, layer3.0.conv.1.bn.weight, layer3.0.conv.1.bn.bias, layer3.0.conv.1.bn.running_mean, layer3.0.conv.1.bn.running_var, layer3.0.conv.2.conv.weight, layer3.0.conv.2.bn.weight, layer3.0.conv.2.bn.bias, layer3.0.conv.2.bn.running_mean, layer3.0.conv.2.bn.running_var, layer3.1.conv.0.net.conv.weight, layer3.1.conv.0.net.bn.weight, layer3.1.conv.0.net.bn.bias, layer3.1.conv.0.net.bn.running_mean, layer3.1.conv.0.net.bn.running_var, layer3.1.conv.1.conv.weight, layer3.1.conv.1.bn.weight, layer3.1.conv.1.bn.bias, layer3.1.conv.1.bn.running_mean, layer3.1.conv.1.bn.running_var, layer3.1.conv.2.conv.weight, layer3.1.conv.2.bn.weight, layer3.1.conv.2.bn.bias, layer3.1.conv.2.bn.running_mean, layer3.1.conv.2.bn.running_var, layer3.2.conv.0.net.conv.weight, layer3.2.conv.0.net.bn.weight, layer3.2.conv.0.net.bn.bias, layer3.2.conv.0.net.bn.running_mean, layer3.2.conv.0.net.bn.running_var, layer3.2.conv.1.conv.weight, layer3.2.conv.1.bn.weight, layer3.2.conv.1.bn.bias, layer3.2.conv.1.bn.running_mean, layer3.2.conv.1.bn.running_var, layer3.2.conv.2.conv.weight, layer3.2.conv.2.bn.weight, layer3.2.conv.2.bn.bias, layer3.2.conv.2.bn.running_mean, layer3.2.conv.2.bn.running_var, layer4.0.conv.0.conv.weight, layer4.0.conv.0.bn.weight, layer4.0.conv.0.bn.bias, layer4.0.conv.0.bn.running_mean, layer4.0.conv.0.bn.running_var, layer4.0.conv.1.conv.weight, layer4.0.conv.1.bn.weight, layer4.0.conv.1.bn.bias, layer4.0.conv.1.bn.running_mean, layer4.0.conv.1.bn.running_var, layer4.0.conv.2.conv.weight, layer4.0.conv.2.bn.weight, layer4.0.conv.2.bn.bias, layer4.0.conv.2.bn.running_mean, layer4.0.conv.2.bn.running_var, layer4.1.conv.0.net.conv.weight, layer4.1.conv.0.net.bn.weight, layer4.1.conv.0.net.bn.bias, layer4.1.conv.0.net.bn.running_mean, layer4.1.conv.0.net.bn.running_var, layer4.1.conv.1.conv.weight, layer4.1.conv.1.bn.weight, layer4.1.conv.1.bn.bias, layer4.1.conv.1.bn.running_mean, layer4.1.conv.1.bn.running_var, layer4.1.conv.2.conv.weight, layer4.1.conv.2.bn.weight, layer4.1.conv.2.bn.bias, layer4.1.conv.2.bn.running_mean, layer4.1.conv.2.bn.running_var, layer4.2.conv.0.net.conv.weight, layer4.2.conv.0.net.bn.weight, layer4.2.conv.0.net.bn.bias, layer4.2.conv.0.net.bn.running_mean, layer4.2.conv.0.net.bn.running_var, layer4.2.conv.1.conv.weight, layer4.2.conv.1.bn.weight, layer4.2.conv.1.bn.bias, layer4.2.conv.1.bn.running_mean, layer4.2.conv.1.bn.running_var, layer4.2.conv.2.conv.weight, layer4.2.conv.2.bn.weight, layer4.2.conv.2.bn.bias, layer4.2.conv.2.bn.running_mean, layer4.2.conv.2.bn.running_var, layer4.3.conv.0.net.conv.weight, layer4.3.conv.0.net.bn.weight, layer4.3.conv.0.net.bn.bias, layer4.3.conv.0.net.bn.running_mean, layer4.3.conv.0.net.bn.running_var, layer4.3.conv.1.conv.weight, layer4.3.conv.1.bn.weight, layer4.3.conv.1.bn.bias, layer4.3.conv.1.bn.running_mean, layer4.3.conv.1.bn.running_var, layer4.3.conv.2.conv.weight, layer4.3.conv.2.bn.weight, layer4.3.conv.2.bn.bias, layer4.3.conv.2.bn.running_mean, layer4.3.conv.2.bn.running_var, layer5.0.conv.0.conv.weight, layer5.0.conv.0.bn.weight, layer5.0.conv.0.bn.bias, layer5.0.conv.0.bn.running_mean, layer5.0.conv.0.bn.running_var, layer5.0.conv.1.conv.weight, layer5.0.conv.1.bn.weight, layer5.0.conv.1.bn.bias, layer5.0.conv.1.bn.running_mean, layer5.0.conv.1.bn.running_var, layer5.0.conv.2.conv.weight, layer5.0.conv.2.bn.weight, layer5.0.conv.2.bn.bias, layer5.0.conv.2.bn.running_mean, layer5.0.conv.2.bn.running_var, layer5.1.conv.0.net.conv.weight, layer5.1.conv.0.net.bn.weight, layer5.1.conv.0.net.bn.bias, layer5.1.conv.0.net.bn.running_mean, layer5.1.conv.0.net.bn.running_var, layer5.1.conv.1.conv.weight, layer5.1.conv.1.bn.weight, layer5.1.conv.1.bn.bias, layer5.1.conv.1.bn.running_mean, layer5.1.conv.1.bn.running_var, layer5.1.conv.2.conv.weight, layer5.1.conv.2.bn.weight, layer5.1.conv.2.bn.bias, layer5.1.conv.2.bn.running_mean, layer5.1.conv.2.bn.running_var, layer5.2.conv.0.net.conv.weight, layer5.2.conv.0.net.bn.weight, layer5.2.conv.0.net.bn.bias, layer5.2.conv.0.net.bn.running_mean, layer5.2.conv.0.net.bn.running_var, layer5.2.conv.1.conv.weight, layer5.2.conv.1.bn.weight, layer5.2.conv.1.bn.bias, layer5.2.conv.1.bn.running_mean, layer5.2.conv.1.bn.running_var, layer5.2.conv.2.conv.weight, layer5.2.conv.2.bn.weight, layer5.2.conv.2.bn.bias, layer5.2.conv.2.bn.running_mean, layer5.2.conv.2.bn.running_var, layer6.0.conv.0.conv.weight, layer6.0.conv.0.bn.weight, layer6.0.conv.0.bn.bias, layer6.0.conv.0.bn.running_mean, layer6.0.conv.0.bn.running_var, layer6.0.conv.1.conv.weight, layer6.0.conv.1.bn.weight, layer6.0.conv.1.bn.bias, layer6.0.conv.1.bn.running_mean, layer6.0.conv.1.bn.running_var, layer6.0.conv.2.conv.weight, layer6.0.conv.2.bn.weight, layer6.0.conv.2.bn.bias, layer6.0.conv.2.bn.running_mean, layer6.0.conv.2.bn.running_var, layer6.1.conv.0.net.conv.weight, layer6.1.conv.0.net.bn.weight, layer6.1.conv.0.net.bn.bias, layer6.1.conv.0.net.bn.running_mean, layer6.1.conv.0.net.bn.running_var, layer6.1.conv.1.conv.weight, layer6.1.conv.1.bn.weight, layer6.1.conv.1.bn.bias, layer6.1.conv.1.bn.running_mean, layer6.1.conv.1.bn.running_var, layer6.1.conv.2.conv.weight, layer6.1.conv.2.bn.weight, layer6.1.conv.2.bn.bias, layer6.1.conv.2.bn.running_mean, layer6.1.conv.2.bn.running_var, layer6.2.conv.0.net.conv.weight, layer6.2.conv.0.net.bn.weight, layer6.2.conv.0.net.bn.bias, layer6.2.conv.0.net.bn.running_mean, layer6.2.conv.0.net.bn.running_var, layer6.2.conv.1.conv.weight, layer6.2.conv.1.bn.weight, layer6.2.conv.1.bn.bias, layer6.2.conv.1.bn.running_mean, layer6.2.conv.1.bn.running_var, layer6.2.conv.2.conv.weight, layer6.2.conv.2.bn.weight, layer6.2.conv.2.bn.bias, layer6.2.conv.2.bn.running_mean, layer6.2.conv.2.bn.running_var, layer7.0.conv.0.conv.weight, layer7.0.conv.0.bn.weight, layer7.0.conv.0.bn.bias, layer7.0.conv.0.bn.running_mean, layer7.0.conv.0.bn.running_var, layer7.0.conv.1.conv.weight, layer7.0.conv.1.bn.weight, layer7.0.conv.1.bn.bias, layer7.0.conv.1.bn.running_mean, layer7.0.conv.1.bn.running_var, layer7.0.conv.2.conv.weight, layer7.0.conv.2.bn.weight, layer7.0.conv.2.bn.bias, layer7.0.conv.2.bn.running_mean, layer7.0.conv.2.bn.running_var, conv2.conv.weight, conv2.bn.weight, conv2.bn.bias, conv2.bn.running_mean, conv2.bn.running_var

Name of parameter - Initialization information

conv1.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.0.conv.weight - torch.Size([32, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.0.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.0.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.1.conv.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.1.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.1.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.0.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.0.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.1.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.1.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.2.conv.weight - torch.Size([24, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.2.bn.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.2.bn.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.0.net.conv.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.0.net.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.0.net.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.1.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.1.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.2.conv.weight - torch.Size([24, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.2.bn.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.2.bn.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.0.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.0.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.1.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.1.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.2.conv.weight - torch.Size([32, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.2.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.2.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.0.net.conv.weight - torch.Size([192, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.0.net.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.0.net.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.1.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.1.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.2.conv.weight - torch.Size([32, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.2.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.2.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.0.net.conv.weight - torch.Size([192, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.0.net.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.0.net.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.1.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.1.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.2.conv.weight - torch.Size([32, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.2.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.2.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.0.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.0.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.1.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.1.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.2.conv.weight - torch.Size([64, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.0.net.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.0.net.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.0.net.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.2.conv.weight - torch.Size([64, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.0.net.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.0.net.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.0.net.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.2.conv.weight - torch.Size([64, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.0.net.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.0.net.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.0.net.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.2.conv.weight - torch.Size([64, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.0.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.0.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.0.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.2.conv.weight - torch.Size([96, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.2.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.2.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.0.net.conv.weight - torch.Size([576, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.0.net.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.0.net.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.1.conv.weight - torch.Size([576, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.1.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.1.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.2.conv.weight - torch.Size([96, 576, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.2.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.2.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.0.net.conv.weight - torch.Size([576, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.0.net.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.0.net.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.1.conv.weight - torch.Size([576, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.1.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.1.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.2.conv.weight - torch.Size([96, 576, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.2.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.2.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.0.conv.weight - torch.Size([576, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.0.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.0.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.1.conv.weight - torch.Size([576, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.1.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.1.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.2.conv.weight - torch.Size([160, 576, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.2.bn.weight - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.2.bn.bias - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.0.net.conv.weight - torch.Size([960, 160, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.0.net.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.0.net.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.1.conv.weight - torch.Size([960, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.1.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.1.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.2.conv.weight - torch.Size([160, 960, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.2.bn.weight - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.2.bn.bias - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.0.net.conv.weight - torch.Size([960, 160, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.0.net.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.0.net.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.1.conv.weight - torch.Size([960, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.1.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.1.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.2.conv.weight - torch.Size([160, 960, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.2.bn.weight - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.2.bn.bias - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.0.conv.weight - torch.Size([960, 160, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.0.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.0.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.1.conv.weight - torch.Size([960, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.1.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.1.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.2.conv.weight - torch.Size([320, 960, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.2.bn.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.2.bn.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

conv2.conv.weight - torch.Size([1280, 320, 1, 1]): 
Initialized by user-defined `init_weights` in ConvModule  

conv2.bn.weight - torch.Size([1280]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

conv2.bn.bias - torch.Size([1280]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  
2025/08/08 11:31:29 - mmengine - INFO - load model from: ./stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth
2025/08/08 11:31:29 - mmengine - INFO - Loads checkpoint by local backend from path: ./stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth
2025/08/08 11:31:29 - mmengine - WARNING - The model and loaded state dict do not match exactly

unexpected key in source state_dict: backbone.data_bn.weight, backbone.data_bn.bias, backbone.data_bn.running_mean, backbone.data_bn.running_var, backbone.data_bn.num_batches_tracked, backbone.gcn.0.gcn.PA, backbone.gcn.0.gcn.A, backbone.gcn.0.gcn.bn.weight, backbone.gcn.0.gcn.bn.bias, backbone.gcn.0.gcn.bn.running_mean, backbone.gcn.0.gcn.bn.running_var, backbone.gcn.0.gcn.bn.num_batches_tracked, backbone.gcn.0.gcn.conv.weight, backbone.gcn.0.gcn.conv.bias, backbone.gcn.0.tcn.conv.weight, backbone.gcn.0.tcn.conv.bias, backbone.gcn.0.tcn.bn.weight, backbone.gcn.0.tcn.bn.bias, backbone.gcn.0.tcn.bn.running_mean, backbone.gcn.0.tcn.bn.running_var, backbone.gcn.0.tcn.bn.num_batches_tracked, backbone.gcn.1.gcn.PA, backbone.gcn.1.gcn.A, backbone.gcn.1.gcn.bn.weight, backbone.gcn.1.gcn.bn.bias, backbone.gcn.1.gcn.bn.running_mean, backbone.gcn.1.gcn.bn.running_var, backbone.gcn.1.gcn.bn.num_batches_tracked, backbone.gcn.1.gcn.conv.weight, backbone.gcn.1.gcn.conv.bias, backbone.gcn.1.tcn.conv.weight, backbone.gcn.1.tcn.conv.bias, backbone.gcn.1.tcn.bn.weight, backbone.gcn.1.tcn.bn.bias, backbone.gcn.1.tcn.bn.running_mean, backbone.gcn.1.tcn.bn.running_var, backbone.gcn.1.tcn.bn.num_batches_tracked, backbone.gcn.2.gcn.PA, backbone.gcn.2.gcn.A, backbone.gcn.2.gcn.bn.weight, backbone.gcn.2.gcn.bn.bias, backbone.gcn.2.gcn.bn.running_mean, backbone.gcn.2.gcn.bn.running_var, backbone.gcn.2.gcn.bn.num_batches_tracked, backbone.gcn.2.gcn.conv.weight, backbone.gcn.2.gcn.conv.bias, backbone.gcn.2.tcn.conv.weight, backbone.gcn.2.tcn.conv.bias, backbone.gcn.2.tcn.bn.weight, backbone.gcn.2.tcn.bn.bias, backbone.gcn.2.tcn.bn.running_mean, backbone.gcn.2.tcn.bn.running_var, backbone.gcn.2.tcn.bn.num_batches_tracked, backbone.gcn.3.gcn.PA, backbone.gcn.3.gcn.A, backbone.gcn.3.gcn.bn.weight, backbone.gcn.3.gcn.bn.bias, backbone.gcn.3.gcn.bn.running_mean, backbone.gcn.3.gcn.bn.running_var, backbone.gcn.3.gcn.bn.num_batches_tracked, backbone.gcn.3.gcn.conv.weight, backbone.gcn.3.gcn.conv.bias, backbone.gcn.3.tcn.conv.weight, backbone.gcn.3.tcn.conv.bias, backbone.gcn.3.tcn.bn.weight, backbone.gcn.3.tcn.bn.bias, backbone.gcn.3.tcn.bn.running_mean, backbone.gcn.3.tcn.bn.running_var, backbone.gcn.3.tcn.bn.num_batches_tracked, backbone.gcn.4.gcn.PA, backbone.gcn.4.gcn.A, backbone.gcn.4.gcn.bn.weight, backbone.gcn.4.gcn.bn.bias, backbone.gcn.4.gcn.bn.running_mean, backbone.gcn.4.gcn.bn.running_var, backbone.gcn.4.gcn.bn.num_batches_tracked, backbone.gcn.4.gcn.conv.weight, backbone.gcn.4.gcn.conv.bias, backbone.gcn.4.tcn.conv.weight, backbone.gcn.4.tcn.conv.bias, backbone.gcn.4.tcn.bn.weight, backbone.gcn.4.tcn.bn.bias, backbone.gcn.4.tcn.bn.running_mean, backbone.gcn.4.tcn.bn.running_var, backbone.gcn.4.tcn.bn.num_batches_tracked, backbone.gcn.4.residual.conv.weight, backbone.gcn.4.residual.conv.bias, backbone.gcn.4.residual.bn.weight, backbone.gcn.4.residual.bn.bias, backbone.gcn.4.residual.bn.running_mean, backbone.gcn.4.residual.bn.running_var, backbone.gcn.4.residual.bn.num_batches_tracked, backbone.gcn.5.gcn.PA, backbone.gcn.5.gcn.A, backbone.gcn.5.gcn.bn.weight, backbone.gcn.5.gcn.bn.bias, backbone.gcn.5.gcn.bn.running_mean, backbone.gcn.5.gcn.bn.running_var, backbone.gcn.5.gcn.bn.num_batches_tracked, backbone.gcn.5.gcn.conv.weight, backbone.gcn.5.gcn.conv.bias, backbone.gcn.5.tcn.conv.weight, backbone.gcn.5.tcn.conv.bias, backbone.gcn.5.tcn.bn.weight, backbone.gcn.5.tcn.bn.bias, backbone.gcn.5.tcn.bn.running_mean, backbone.gcn.5.tcn.bn.running_var, backbone.gcn.5.tcn.bn.num_batches_tracked, backbone.gcn.6.gcn.PA, backbone.gcn.6.gcn.A, backbone.gcn.6.gcn.bn.weight, backbone.gcn.6.gcn.bn.bias, backbone.gcn.6.gcn.bn.running_mean, backbone.gcn.6.gcn.bn.running_var, backbone.gcn.6.gcn.bn.num_batches_tracked, backbone.gcn.6.gcn.conv.weight, backbone.gcn.6.gcn.conv.bias, backbone.gcn.6.tcn.conv.weight, backbone.gcn.6.tcn.conv.bias, backbone.gcn.6.tcn.bn.weight, backbone.gcn.6.tcn.bn.bias, backbone.gcn.6.tcn.bn.running_mean, backbone.gcn.6.tcn.bn.running_var, backbone.gcn.6.tcn.bn.num_batches_tracked, backbone.gcn.7.gcn.PA, backbone.gcn.7.gcn.A, backbone.gcn.7.gcn.bn.weight, backbone.gcn.7.gcn.bn.bias, backbone.gcn.7.gcn.bn.running_mean, backbone.gcn.7.gcn.bn.running_var, backbone.gcn.7.gcn.bn.num_batches_tracked, backbone.gcn.7.gcn.conv.weight, backbone.gcn.7.gcn.conv.bias, backbone.gcn.7.tcn.conv.weight, backbone.gcn.7.tcn.conv.bias, backbone.gcn.7.tcn.bn.weight, backbone.gcn.7.tcn.bn.bias, backbone.gcn.7.tcn.bn.running_mean, backbone.gcn.7.tcn.bn.running_var, backbone.gcn.7.tcn.bn.num_batches_tracked, backbone.gcn.7.residual.conv.weight, backbone.gcn.7.residual.conv.bias, backbone.gcn.7.residual.bn.weight, backbone.gcn.7.residual.bn.bias, backbone.gcn.7.residual.bn.running_mean, backbone.gcn.7.residual.bn.running_var, backbone.gcn.7.residual.bn.num_batches_tracked, backbone.gcn.8.gcn.PA, backbone.gcn.8.gcn.A, backbone.gcn.8.gcn.bn.weight, backbone.gcn.8.gcn.bn.bias, backbone.gcn.8.gcn.bn.running_mean, backbone.gcn.8.gcn.bn.running_var, backbone.gcn.8.gcn.bn.num_batches_tracked, backbone.gcn.8.gcn.conv.weight, backbone.gcn.8.gcn.conv.bias, backbone.gcn.8.tcn.conv.weight, backbone.gcn.8.tcn.conv.bias, backbone.gcn.8.tcn.bn.weight, backbone.gcn.8.tcn.bn.bias, backbone.gcn.8.tcn.bn.running_mean, backbone.gcn.8.tcn.bn.running_var, backbone.gcn.8.tcn.bn.num_batches_tracked, backbone.gcn.9.gcn.PA, backbone.gcn.9.gcn.A, backbone.gcn.9.gcn.bn.weight, backbone.gcn.9.gcn.bn.bias, backbone.gcn.9.gcn.bn.running_mean, backbone.gcn.9.gcn.bn.running_var, backbone.gcn.9.gcn.bn.num_batches_tracked, backbone.gcn.9.gcn.conv.weight, backbone.gcn.9.gcn.conv.bias, backbone.gcn.9.tcn.conv.weight, backbone.gcn.9.tcn.conv.bias, backbone.gcn.9.tcn.bn.weight, backbone.gcn.9.tcn.bn.bias, backbone.gcn.9.tcn.bn.running_mean, backbone.gcn.9.tcn.bn.running_var, backbone.gcn.9.tcn.bn.num_batches_tracked, cls_head.fc.weight, cls_head.fc.bias

missing keys in source state_dict: data_bn.weight, data_bn.bias, data_bn.running_mean, data_bn.running_var, gcn.0.gcn.PA, gcn.0.gcn.A, gcn.0.gcn.bn.weight, gcn.0.gcn.bn.bias, gcn.0.gcn.bn.running_mean, gcn.0.gcn.bn.running_var, gcn.0.gcn.conv.weight, gcn.0.gcn.conv.bias, gcn.0.tcn.conv.weight, gcn.0.tcn.conv.bias, gcn.0.tcn.bn.weight, gcn.0.tcn.bn.bias, gcn.0.tcn.bn.running_mean, gcn.0.tcn.bn.running_var, gcn.1.gcn.PA, gcn.1.gcn.A, gcn.1.gcn.bn.weight, gcn.1.gcn.bn.bias, gcn.1.gcn.bn.running_mean, gcn.1.gcn.bn.running_var, gcn.1.gcn.conv.weight, gcn.1.gcn.conv.bias, gcn.1.tcn.conv.weight, gcn.1.tcn.conv.bias, gcn.1.tcn.bn.weight, gcn.1.tcn.bn.bias, gcn.1.tcn.bn.running_mean, gcn.1.tcn.bn.running_var, gcn.2.gcn.PA, gcn.2.gcn.A, gcn.2.gcn.bn.weight, gcn.2.gcn.bn.bias, gcn.2.gcn.bn.running_mean, gcn.2.gcn.bn.running_var, gcn.2.gcn.conv.weight, gcn.2.gcn.conv.bias, gcn.2.tcn.conv.weight, gcn.2.tcn.conv.bias, gcn.2.tcn.bn.weight, gcn.2.tcn.bn.bias, gcn.2.tcn.bn.running_mean, gcn.2.tcn.bn.running_var, gcn.3.gcn.PA, gcn.3.gcn.A, gcn.3.gcn.bn.weight, gcn.3.gcn.bn.bias, gcn.3.gcn.bn.running_mean, gcn.3.gcn.bn.running_var, gcn.3.gcn.conv.weight, gcn.3.gcn.conv.bias, gcn.3.tcn.conv.weight, gcn.3.tcn.conv.bias, gcn.3.tcn.bn.weight, gcn.3.tcn.bn.bias, gcn.3.tcn.bn.running_mean, gcn.3.tcn.bn.running_var, gcn.4.gcn.PA, gcn.4.gcn.A, gcn.4.gcn.bn.weight, gcn.4.gcn.bn.bias, gcn.4.gcn.bn.running_mean, gcn.4.gcn.bn.running_var, gcn.4.gcn.conv.weight, gcn.4.gcn.conv.bias, gcn.4.tcn.conv.weight, gcn.4.tcn.conv.bias, gcn.4.tcn.bn.weight, gcn.4.tcn.bn.bias, gcn.4.tcn.bn.running_mean, gcn.4.tcn.bn.running_var, gcn.4.residual.conv.weight, gcn.4.residual.conv.bias, gcn.4.residual.bn.weight, gcn.4.residual.bn.bias, gcn.4.residual.bn.running_mean, gcn.4.residual.bn.running_var, gcn.5.gcn.PA, gcn.5.gcn.A, gcn.5.gcn.bn.weight, gcn.5.gcn.bn.bias, gcn.5.gcn.bn.running_mean, gcn.5.gcn.bn.running_var, gcn.5.gcn.conv.weight, gcn.5.gcn.conv.bias, gcn.5.tcn.conv.weight, gcn.5.tcn.conv.bias, gcn.5.tcn.bn.weight, gcn.5.tcn.bn.bias, gcn.5.tcn.bn.running_mean, gcn.5.tcn.bn.running_var, gcn.6.gcn.PA, gcn.6.gcn.A, gcn.6.gcn.bn.weight, gcn.6.gcn.bn.bias, gcn.6.gcn.bn.running_mean, gcn.6.gcn.bn.running_var, gcn.6.gcn.conv.weight, gcn.6.gcn.conv.bias, gcn.6.tcn.conv.weight, gcn.6.tcn.conv.bias, gcn.6.tcn.bn.weight, gcn.6.tcn.bn.bias, gcn.6.tcn.bn.running_mean, gcn.6.tcn.bn.running_var, gcn.7.gcn.PA, gcn.7.gcn.A, gcn.7.gcn.bn.weight, gcn.7.gcn.bn.bias, gcn.7.gcn.bn.running_mean, gcn.7.gcn.bn.running_var, gcn.7.gcn.conv.weight, gcn.7.gcn.conv.bias, gcn.7.tcn.conv.weight, gcn.7.tcn.conv.bias, gcn.7.tcn.bn.weight, gcn.7.tcn.bn.bias, gcn.7.tcn.bn.running_mean, gcn.7.tcn.bn.running_var, gcn.7.residual.conv.weight, gcn.7.residual.conv.bias, gcn.7.residual.bn.weight, gcn.7.residual.bn.bias, gcn.7.residual.bn.running_mean, gcn.7.residual.bn.running_var, gcn.8.gcn.PA, gcn.8.gcn.A, gcn.8.gcn.bn.weight, gcn.8.gcn.bn.bias, gcn.8.gcn.bn.running_mean, gcn.8.gcn.bn.running_var, gcn.8.gcn.conv.weight, gcn.8.gcn.conv.bias, gcn.8.tcn.conv.weight, gcn.8.tcn.conv.bias, gcn.8.tcn.bn.weight, gcn.8.tcn.bn.bias, gcn.8.tcn.bn.running_mean, gcn.8.tcn.bn.running_var, gcn.9.gcn.PA, gcn.9.gcn.A, gcn.9.gcn.bn.weight, gcn.9.gcn.bn.bias, gcn.9.gcn.bn.running_mean, gcn.9.gcn.bn.running_var, gcn.9.gcn.conv.weight, gcn.9.gcn.conv.bias, gcn.9.tcn.conv.weight, gcn.9.tcn.conv.bias, gcn.9.tcn.bn.weight, gcn.9.tcn.bn.bias, gcn.9.tcn.bn.running_mean, gcn.9.tcn.bn.running_var

Name of parameter - Initialization information

data_bn.weight - torch.Size([51]): 
The value is the same before and after calling `init_weights` of STGCN  

data_bn.bias - torch.Size([51]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.conv.weight - torch.Size([192, 3, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.0.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.0.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.conv.weight - torch.Size([192, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.1.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.1.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.conv.weight - torch.Size([192, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.2.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.2.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.conv.weight - torch.Size([192, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.3.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.3.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.conv.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.tcn.conv.weight - torch.Size([128, 128, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.tcn.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.tcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.tcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.residual.conv.weight - torch.Size([128, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.residual.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.residual.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.residual.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.conv.weight - torch.Size([384, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.conv.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.tcn.conv.weight - torch.Size([128, 128, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.5.tcn.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.5.tcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.tcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.conv.weight - torch.Size([384, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.conv.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.tcn.conv.weight - torch.Size([128, 128, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.6.tcn.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.6.tcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.tcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.conv.weight - torch.Size([768, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.conv.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.tcn.conv.weight - torch.Size([256, 256, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.tcn.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.tcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.tcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.residual.conv.weight - torch.Size([256, 128, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.residual.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.residual.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.residual.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.conv.weight - torch.Size([768, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.conv.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.tcn.conv.weight - torch.Size([256, 256, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.8.tcn.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.8.tcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.tcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.conv.weight - torch.Size([768, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.conv.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.tcn.conv.weight - torch.Size([256, 256, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.9.tcn.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.9.tcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.tcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  
2025/08/08 11:31:29 - mmengine - INFO - Initial class counts: [6258, 5494, 4751]
2025/08/08 11:31:29 - mmengine - INFO - Initial class weights: [0.8790348354106743, 1.2015289406625411, 1.273647653125658]
2025/08/08 11:31:29 - mmengine - INFO - HardSampleHook initialized with threshold=0.7
2025/08/08 11:31:29 - mmengine - INFO - Hard sample weight multiplier: 2.0
2025/08/08 11:31:29 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/08/08 11:31:29 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/08/08 11:31:29 - mmengine - INFO - Checkpoints will be saved to /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal.
2025/08/08 11:40:54 - mmengine - INFO - Epoch(train)   [1][20/28]  base_lr: 1.0000e-03 lr: 1.0000e-03  eta: 21:48:24  time: 28.2392  data_time: 16.0329  memory: 30737  grad_norm: 7.7773  loss: 0.5500  top1_acc: 0.3594  top5_acc: 1.0000  loss_cls: 0.5500
2025/08/08 11:41:36 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:41:36 - mmengine - INFO - Epoch(train)   [1][28/28]  base_lr: 1.0000e-03 lr: 1.0000e-03  eta: 16:40:34  time: 18.6353  data_time: 9.7598  memory: 30731  grad_norm: 7.3265  loss: 0.5464  top1_acc: 0.4074  top5_acc: 1.0000  loss_cls: 0.5464
2025/08/08 11:41:36 - mmengine - INFO - Saving checkpoint at 1 epochs
2025/08/08 11:43:44 - mmengine - INFO - Epoch(val) [1][10/10]    acc/top1: 0.3331  acc/top5: 1.0000  acc/mean1: 0.3333  data_time: 12.0214  time: 12.1038
2025/08/08 11:43:47 - mmengine - INFO - The best checkpoint with 0.3331 acc/top1 at 1 epoch is saved to best_acc_top1_epoch_1.pth.
2025/08/08 11:45:33 - mmengine - INFO - Epoch(train)   [2][20/28]  base_lr: 3.2500e-03 lr: 3.2500e-03  eta: 11:17:25  time: 5.1265  data_time: 4.4145  memory: 30731  grad_norm: 5.5166  loss: 0.5392  top1_acc: 0.3281  top5_acc: 1.0000  loss_cls: 0.5392
2025/08/08 11:45:48 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:45:48 - mmengine - INFO - Epoch(train)   [2][28/28]  base_lr: 3.2500e-03 lr: 3.2500e-03  eta: 9:50:54  time: 3.9301  data_time: 3.2392  memory: 30731  grad_norm: 5.3945  loss: 0.5364  top1_acc: 0.3519  top5_acc: 1.0000  loss_cls: 0.5364
2025/08/08 11:45:48 - mmengine - INFO - Saving checkpoint at 2 epochs
2025/08/08 11:46:18 - mmengine - INFO - Epoch(val) [2][10/10]    acc/top1: 0.3834  acc/top5: 1.0000  acc/mean1: 0.3374  data_time: 2.3301  time: 2.4095
2025/08/08 11:46:19 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_1.pth is removed
2025/08/08 11:46:22 - mmengine - INFO - The best checkpoint with 0.3834 acc/top1 at 2 epoch is saved to best_acc_top1_epoch_2.pth.
2025/08/08 11:47:48 - mmengine - INFO - Epoch(train)   [3][20/28]  base_lr: 5.5000e-03 lr: 5.5000e-03  eta: 8:01:37  time: 4.1336  data_time: 3.4002  memory: 30731  grad_norm: 5.6275  loss: 0.5318  top1_acc: 0.3984  top5_acc: 1.0000  loss_cls: 0.5318
2025/08/08 11:47:59 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:47:59 - mmengine - INFO - Epoch(train)   [3][28/28]  base_lr: 5.5000e-03 lr: 5.5000e-03  eta: 7:20:47  time: 2.9412  data_time: 2.2669  memory: 30731  grad_norm: 6.3296  loss: 0.5290  top1_acc: 0.3889  top5_acc: 1.0000  loss_cls: 0.5290
2025/08/08 11:47:59 - mmengine - INFO - Saving checkpoint at 3 epochs
2025/08/08 11:48:19 - mmengine - INFO - Epoch(val) [3][10/10]    acc/top1: 0.4276  acc/top5: 1.0000  acc/mean1: 0.3867  data_time: 1.2518  time: 1.3349
2025/08/08 11:48:19 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_2.pth is removed
2025/08/08 11:48:22 - mmengine - INFO - The best checkpoint with 0.4276 acc/top1 at 3 epoch is saved to best_acc_top1_epoch_3.pth.
2025/08/08 11:49:40 - mmengine - INFO - Epoch(train)   [4][20/28]  base_lr: 7.7500e-03 lr: 7.7500e-03  eta: 6:25:13  time: 3.6815  data_time: 2.8792  memory: 30731  grad_norm: 7.4535  loss: 0.4858  top1_acc: 0.5625  top5_acc: 1.0000  loss_cls: 0.4858
2025/08/08 11:49:48 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:49:48 - mmengine - INFO - Epoch(train)   [4][28/28]  base_lr: 7.7500e-03 lr: 7.7500e-03  eta: 5:59:57  time: 2.5115  data_time: 1.7707  memory: 30731  grad_norm: 8.0458  loss: 0.4455  top1_acc: 0.5000  top5_acc: 1.0000  loss_cls: 0.4455
2025/08/08 11:49:48 - mmengine - INFO - Saving checkpoint at 4 epochs
2025/08/08 11:50:06 - mmengine - INFO - Epoch(val) [4][10/10]    acc/top1: 0.6341  acc/top5: 1.0000  acc/mean1: 0.6274  data_time: 1.1681  time: 1.2490
2025/08/08 11:50:06 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_3.pth is removed
2025/08/08 11:50:10 - mmengine - INFO - The best checkpoint with 0.6341 acc/top1 at 4 epoch is saved to best_acc_top1_epoch_4.pth.
2025/08/08 11:51:40 - mmengine - INFO - Epoch(train)   [5][20/28]  base_lr: 1.0000e-02 lr: 1.0000e-02  eta: 5:32:06  time: 4.2980  data_time: 3.5150  memory: 30731  grad_norm: 6.4953  loss: 0.3376  top1_acc: 0.6094  top5_acc: 1.0000  loss_cls: 0.3376
2025/08/08 11:51:48 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:51:48 - mmengine - INFO - Epoch(train)   [5][28/28]  base_lr: 1.0000e-02 lr: 1.0000e-02  eta: 5:14:39  time: 3.0956  data_time: 2.3673  memory: 30731  grad_norm: 6.2370  loss: 0.3207  top1_acc: 0.7037  top5_acc: 1.0000  loss_cls: 0.3207
2025/08/08 11:51:48 - mmengine - INFO - Saving checkpoint at 5 epochs
2025/08/08 11:52:05 - mmengine - INFO - Epoch(val) [5][10/10]    acc/top1: 0.7014  acc/top5: 1.0000  acc/mean1: 0.7072  data_time: 1.1658  time: 1.2464
2025/08/08 11:52:06 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_4.pth is removed
2025/08/08 11:52:10 - mmengine - INFO - The best checkpoint with 0.7014 acc/top1 at 5 epoch is saved to best_acc_top1_epoch_5.pth.
2025/08/08 11:53:33 - mmengine - INFO - Epoch(train)   [6][20/28]  base_lr: 1.0000e-02 lr: 1.0000e-02  eta: 4:55:09  time: 3.9831  data_time: 3.1831  memory: 30731  grad_norm: 5.5216  loss: 0.2831  top1_acc: 0.7031  top5_acc: 1.0000  loss_cls: 0.2831
2025/08/08 11:53:41 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:53:41 - mmengine - INFO - Epoch(train)   [6][28/28]  base_lr: 1.0000e-02 lr: 1.0000e-02  eta: 4:42:18  time: 2.7962  data_time: 2.0872  memory: 30731  grad_norm: 5.4933  loss: 0.2712  top1_acc: 0.7407  top5_acc: 1.0000  loss_cls: 0.2712
2025/08/08 11:53:42 - mmengine - INFO - Saving checkpoint at 6 epochs
2025/08/08 11:53:58 - mmengine - INFO - Epoch(val) [6][10/10]    acc/top1: 0.7708  acc/top5: 1.0000  acc/mean1: 0.7703  data_time: 1.0368  time: 1.1172
2025/08/08 11:53:59 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_5.pth is removed
2025/08/08 11:54:03 - mmengine - INFO - The best checkpoint with 0.7708 acc/top1 at 6 epoch is saved to best_acc_top1_epoch_6.pth.
2025/08/08 11:55:27 - mmengine - INFO - Epoch(train)   [7][20/28]  base_lr: 9.9973e-03 lr: 9.9973e-03  eta: 4:28:44  time: 3.9698  data_time: 3.2517  memory: 30731  grad_norm: 5.2715  loss: 0.2406  top1_acc: 0.8516  top5_acc: 1.0000  loss_cls: 0.2406
2025/08/08 11:55:36 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:55:36 - mmengine - INFO - Epoch(train)   [7][28/28]  base_lr: 9.9973e-03 lr: 9.9973e-03  eta: 4:19:06  time: 2.9143  data_time: 2.2094  memory: 30731  grad_norm: 5.3828  loss: 0.2360  top1_acc: 0.7778  top5_acc: 1.0000  loss_cls: 0.2360
2025/08/08 11:55:37 - mmengine - INFO - Saving checkpoint at 7 epochs
2025/08/08 11:55:55 - mmengine - INFO - Epoch(val) [7][10/10]    acc/top1: 0.7911  acc/top5: 1.0000  acc/mean1: 0.7993  data_time: 1.3147  time: 1.3967
2025/08/08 11:55:56 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_6.pth is removed
2025/08/08 11:56:00 - mmengine - INFO - The best checkpoint with 0.7911 acc/top1 at 7 epoch is saved to best_acc_top1_epoch_7.pth.
2025/08/08 11:57:25 - mmengine - INFO - Epoch(train)   [8][20/28]  base_lr: 9.9891e-03 lr: 9.9891e-03  eta: 4:09:28  time: 4.0507  data_time: 3.2310  memory: 30731  grad_norm: 5.1783  loss: 0.2175  top1_acc: 0.7734  top5_acc: 1.0000  loss_cls: 0.2175
2025/08/08 11:57:33 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:57:33 - mmengine - INFO - Epoch(train)   [8][28/28]  base_lr: 9.9891e-03 lr: 9.9891e-03  eta: 4:01:19  time: 2.6376  data_time: 1.8828  memory: 30731  grad_norm: 5.1650  loss: 0.2209  top1_acc: 0.7593  top5_acc: 1.0000  loss_cls: 0.2209
2025/08/08 11:57:33 - mmengine - INFO - Saving checkpoint at 8 epochs
2025/08/08 11:57:50 - mmengine - INFO - Epoch(val) [8][10/10]    acc/top1: 0.8110  acc/top5: 1.0000  acc/mean1: 0.8123  data_time: 1.0668  time: 1.1471
2025/08/08 11:57:50 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_7.pth is removed
2025/08/08 11:57:54 - mmengine - INFO - The best checkpoint with 0.8110 acc/top1 at 8 epoch is saved to best_acc_top1_epoch_8.pth.
2025/08/08 11:59:16 - mmengine - INFO - Epoch(train)   [9][20/28]  base_lr: 9.9754e-03 lr: 9.9754e-03  eta: 3:53:33  time: 3.9333  data_time: 3.1514  memory: 30731  grad_norm: 4.6016  loss: 0.2048  top1_acc: 0.8047  top5_acc: 1.0000  loss_cls: 0.2048
2025/08/08 11:59:26 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 11:59:26 - mmengine - INFO - Epoch(train)   [9][28/28]  base_lr: 9.9754e-03 lr: 9.9754e-03  eta: 3:47:05  time: 2.7357  data_time: 2.0197  memory: 30731  grad_norm: 4.7297  loss: 0.2029  top1_acc: 0.7593  top5_acc: 1.0000  loss_cls: 0.2029
2025/08/08 11:59:27 - mmengine - INFO - Saving checkpoint at 9 epochs
2025/08/08 11:59:44 - mmengine - INFO - Epoch(val) [9][10/10]    acc/top1: 0.8231  acc/top5: 1.0000  acc/mean1: 0.8249  data_time: 1.1254  time: 1.2074
2025/08/08 11:59:44 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_8.pth is removed
2025/08/08 11:59:47 - mmengine - INFO - The best checkpoint with 0.8231 acc/top1 at 9 epoch is saved to best_acc_top1_epoch_9.pth.
2025/08/08 12:01:12 - mmengine - INFO - Epoch(train)  [10][20/28]  base_lr: 9.9563e-03 lr: 9.9563e-03  eta: 3:41:06  time: 3.9945  data_time: 3.2187  memory: 30731  grad_norm: 4.4398  loss: 0.1913  top1_acc: 0.8359  top5_acc: 1.0000  loss_cls: 0.1913
2025/08/08 12:01:18 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:01:18 - mmengine - INFO - Epoch(train)  [10][28/28]  base_lr: 9.9563e-03 lr: 9.9563e-03  eta: 3:35:03  time: 2.4979  data_time: 1.7926  memory: 30731  grad_norm: 4.3844  loss: 0.1919  top1_acc: 0.8333  top5_acc: 1.0000  loss_cls: 0.1919
2025/08/08 12:01:18 - mmengine - INFO - Saving checkpoint at 10 epochs
2025/08/08 12:01:32 - mmengine - INFO - Epoch(val) [10][10/10]    acc/top1: 0.8426  acc/top5: 1.0000  acc/mean1: 0.8477  data_time: 0.8500  time: 0.9319
2025/08/08 12:01:32 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_9.pth is removed
2025/08/08 12:01:36 - mmengine - INFO - The best checkpoint with 0.8426 acc/top1 at 10 epoch is saved to best_acc_top1_epoch_10.pth.
2025/08/08 12:02:46 - mmengine - INFO - Epoch(train)  [11][20/28]  base_lr: 9.9318e-03 lr: 9.9318e-03  eta: 3:28:16  time: 3.2947  data_time: 2.4555  memory: 30731  grad_norm: 4.2043  loss: 0.1820  top1_acc: 0.8125  top5_acc: 1.0000  loss_cls: 0.1820
2025/08/08 12:02:52 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:02:52 - mmengine - INFO - Epoch(train)  [11][28/28]  base_lr: 9.9318e-03 lr: 9.9318e-03  eta: 3:23:00  time: 2.1078  data_time: 1.3684  memory: 30731  grad_norm: 4.2975  loss: 0.1822  top1_acc: 0.8519  top5_acc: 1.0000  loss_cls: 0.1822
2025/08/08 12:02:52 - mmengine - INFO - Saving checkpoint at 11 epochs
2025/08/08 12:03:06 - mmengine - INFO - Epoch(val) [11][10/10]    acc/top1: 0.8515  acc/top5: 1.0000  acc/mean1: 0.8559  data_time: 0.8904  time: 0.9719
2025/08/08 12:03:06 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_10.pth is removed
2025/08/08 12:03:10 - mmengine - INFO - The best checkpoint with 0.8515 acc/top1 at 11 epoch is saved to best_acc_top1_epoch_11.pth.
2025/08/08 12:04:20 - mmengine - INFO - Epoch(train)  [12][20/28]  base_lr: 9.9019e-03 lr: 9.9019e-03  eta: 3:17:17  time: 3.2603  data_time: 2.4929  memory: 30731  grad_norm: 4.2405  loss: 0.1705  top1_acc: 0.8672  top5_acc: 1.0000  loss_cls: 0.1705
2025/08/08 12:04:26 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:04:26 - mmengine - INFO - Epoch(train)  [12][28/28]  base_lr: 9.9019e-03 lr: 9.9019e-03  eta: 3:12:41  time: 2.0955  data_time: 1.3815  memory: 30731  grad_norm: 4.3530  loss: 0.1608  top1_acc: 0.8519  top5_acc: 1.0000  loss_cls: 0.1608
2025/08/08 12:04:26 - mmengine - INFO - Saving checkpoint at 12 epochs
2025/08/08 12:04:40 - mmengine - INFO - Epoch(val) [12][10/10]    acc/top1: 0.8361  acc/top5: 1.0000  acc/mean1: 0.8421  data_time: 0.8423  time: 0.9229
2025/08/08 12:05:52 - mmengine - INFO - Epoch(train)  [13][20/28]  base_lr: 9.8666e-03 lr: 9.8666e-03  eta: 3:08:35  time: 3.5867  data_time: 2.7693  memory: 30731  grad_norm: 4.3482  loss: 0.1836  top1_acc: 0.8359  top5_acc: 1.0000  loss_cls: 0.1836
2025/08/08 12:06:00 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:06:00 - mmengine - INFO - Epoch(train)  [13][28/28]  base_lr: 9.8666e-03 lr: 9.8666e-03  eta: 3:04:44  time: 2.3966  data_time: 1.6566  memory: 30731  grad_norm: 4.7384  loss: 0.1801  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.1801
2025/08/08 12:06:00 - mmengine - INFO - Saving checkpoint at 13 epochs
2025/08/08 12:06:14 - mmengine - INFO - Epoch(val) [13][10/10]    acc/top1: 0.8381  acc/top5: 1.0000  acc/mean1: 0.8423  data_time: 0.7053  time: 0.7868
2025/08/08 12:07:22 - mmengine - INFO - Epoch(train)  [14][20/28]  base_lr: 9.8260e-03 lr: 9.8260e-03  eta: 3:00:44  time: 3.3704  data_time: 2.5392  memory: 30731  grad_norm: 4.1455  loss: 0.1904  top1_acc: 0.7969  top5_acc: 1.0000  loss_cls: 0.1904
2025/08/08 12:07:30 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:07:30 - mmengine - INFO - Epoch(train)  [14][28/28]  base_lr: 9.8260e-03 lr: 9.8260e-03  eta: 2:57:17  time: 2.3685  data_time: 1.5600  memory: 30731  grad_norm: 4.0566  loss: 0.1833  top1_acc: 0.8704  top5_acc: 1.0000  loss_cls: 0.1833
2025/08/08 12:07:30 - mmengine - INFO - Saving checkpoint at 14 epochs
2025/08/08 12:07:43 - mmengine - INFO - Epoch(val) [14][10/10]    acc/top1: 0.8385  acc/top5: 1.0000  acc/mean1: 0.8401  data_time: 0.6907  time: 0.7745
2025/08/08 12:08:56 - mmengine - INFO - Epoch(train)  [15][20/28]  base_lr: 9.7802e-03 lr: 9.7802e-03  eta: 2:54:14  time: 3.6017  data_time: 2.7897  memory: 30731  grad_norm: 4.0418  loss: 0.1631  top1_acc: 0.8359  top5_acc: 1.0000  loss_cls: 0.1631
2025/08/08 12:09:05 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:09:05 - mmengine - INFO - Epoch(train)  [15][28/28]  base_lr: 9.7802e-03 lr: 9.7802e-03  eta: 2:51:12  time: 2.6532  data_time: 1.9075  memory: 30731  grad_norm: 3.9803  loss: 0.1667  top1_acc: 0.8519  top5_acc: 1.0000  loss_cls: 0.1667
2025/08/08 12:09:05 - mmengine - INFO - Saving checkpoint at 15 epochs
2025/08/08 12:09:20 - mmengine - INFO - Epoch(val) [15][10/10]    acc/top1: 0.8369  acc/top5: 1.0000  acc/mean1: 0.8346  data_time: 0.8033  time: 0.8844
2025/08/08 12:10:29 - mmengine - INFO - Epoch(train)  [16][20/28]  base_lr: 9.7291e-03 lr: 9.7291e-03  eta: 2:48:16  time: 3.4785  data_time: 2.6399  memory: 30731  grad_norm: 3.9014  loss: 0.1617  top1_acc: 0.8359  top5_acc: 1.0000  loss_cls: 0.1617
2025/08/08 12:10:40 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:10:40 - mmengine - INFO - Epoch(train)  [16][28/28]  base_lr: 9.7291e-03 lr: 9.7291e-03  eta: 2:45:35  time: 2.5264  data_time: 1.7586  memory: 30731  grad_norm: 4.0968  loss: 0.1607  top1_acc: 0.8333  top5_acc: 1.0000  loss_cls: 0.1607
2025/08/08 12:10:40 - mmengine - INFO - Saving checkpoint at 16 epochs
2025/08/08 12:10:56 - mmengine - INFO - Epoch(val) [16][10/10]    acc/top1: 0.8572  acc/top5: 1.0000  acc/mean1: 0.8596  data_time: 0.9105  time: 0.9908
2025/08/08 12:10:56 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_11.pth is removed
2025/08/08 12:11:00 - mmengine - INFO - The best checkpoint with 0.8572 acc/top1 at 16 epoch is saved to best_acc_top1_epoch_16.pth.
2025/08/08 12:12:19 - mmengine - INFO - Epoch(train)  [17][20/28]  base_lr: 9.6728e-03 lr: 9.6728e-03  eta: 2:43:21  time: 3.7180  data_time: 2.9767  memory: 30731  grad_norm: 3.8140  loss: 0.1583  top1_acc: 0.8906  top5_acc: 1.0000  loss_cls: 0.1583
2025/08/08 12:12:26 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:12:26 - mmengine - INFO - Epoch(train)  [17][28/28]  base_lr: 9.6728e-03 lr: 9.6728e-03  eta: 2:40:36  time: 2.5186  data_time: 1.8261  memory: 30731  grad_norm: 3.8052  loss: 0.1611  top1_acc: 0.8704  top5_acc: 1.0000  loss_cls: 0.1611
2025/08/08 12:12:27 - mmengine - INFO - Saving checkpoint at 17 epochs
2025/08/08 12:12:43 - mmengine - INFO - Epoch(val) [17][10/10]    acc/top1: 0.8458  acc/top5: 1.0000  acc/mean1: 0.8456  data_time: 1.0192  time: 1.0988
2025/08/08 12:13:57 - mmengine - INFO - Epoch(train)  [18][20/28]  base_lr: 9.6114e-03 lr: 9.6114e-03  eta: 2:38:28  time: 3.6612  data_time: 2.8459  memory: 30731  grad_norm: 3.7968  loss: 0.1498  top1_acc: 0.8438  top5_acc: 1.0000  loss_cls: 0.1498
2025/08/08 12:14:06 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:14:06 - mmengine - INFO - Epoch(train)  [18][28/28]  base_lr: 9.6114e-03 lr: 9.6114e-03  eta: 2:36:06  time: 2.4965  data_time: 1.7640  memory: 30731  grad_norm: 3.9306  loss: 0.1517  top1_acc: 0.8704  top5_acc: 1.0000  loss_cls: 0.1517
2025/08/08 12:14:06 - mmengine - INFO - Saving checkpoint at 18 epochs
2025/08/08 12:14:22 - mmengine - INFO - Epoch(val) [18][10/10]    acc/top1: 0.8296  acc/top5: 1.0000  acc/mean1: 0.8294  data_time: 0.9622  time: 1.0534
2025/08/08 12:15:35 - mmengine - INFO - Epoch(train)  [19][20/28]  base_lr: 9.5450e-03 lr: 9.5450e-03  eta: 2:34:10  time: 3.6795  data_time: 2.8027  memory: 30731  grad_norm: 3.5969  loss: 0.1421  top1_acc: 0.8750  top5_acc: 1.0000  loss_cls: 0.1421
2025/08/08 12:15:44 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:15:44 - mmengine - INFO - Epoch(train)  [19][28/28]  base_lr: 9.5450e-03 lr: 9.5450e-03  eta: 2:31:56  time: 2.5576  data_time: 1.7900  memory: 30731  grad_norm: 3.9151  loss: 0.1503  top1_acc: 0.7963  top5_acc: 1.0000  loss_cls: 0.1503
2025/08/08 12:15:44 - mmengine - INFO - Saving checkpoint at 19 epochs
2025/08/08 12:15:59 - mmengine - INFO - Epoch(val) [19][10/10]    acc/top1: 0.8523  acc/top5: 1.0000  acc/mean1: 0.8562  data_time: 0.8353  time: 0.9158
2025/08/08 12:17:08 - mmengine - INFO - Epoch(train)  [20][20/28]  base_lr: 9.4736e-03 lr: 9.4736e-03  eta: 2:29:49  time: 3.4468  data_time: 2.6357  memory: 30731  grad_norm: 3.5198  loss: 0.1532  top1_acc: 0.8828  top5_acc: 1.0000  loss_cls: 0.1532
2025/08/08 12:17:16 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:17:16 - mmengine - INFO - Epoch(train)  [20][28/28]  base_lr: 9.4736e-03 lr: 9.4736e-03  eta: 2:27:42  time: 2.4157  data_time: 1.6389  memory: 30731  grad_norm: 3.7387  loss: 0.1538  top1_acc: 0.7778  top5_acc: 1.0000  loss_cls: 0.1538
2025/08/08 12:17:17 - mmengine - INFO - Saving checkpoint at 20 epochs
2025/08/08 12:17:31 - mmengine - INFO - Epoch(val) [20][10/10]    acc/top1: 0.8661  acc/top5: 1.0000  acc/mean1: 0.8657  data_time: 0.8100  time: 0.8900
2025/08/08 12:17:31 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_16.pth is removed
2025/08/08 12:17:35 - mmengine - INFO - The best checkpoint with 0.8661 acc/top1 at 20 epoch is saved to best_acc_top1_epoch_20.pth.
2025/08/08 12:18:44 - mmengine - INFO - Epoch(train)  [21][20/28]  base_lr: 9.3974e-03 lr: 9.3974e-03  eta: 2:25:27  time: 3.2292  data_time: 2.4769  memory: 30731  grad_norm: 3.5293  loss: 0.1456  top1_acc: 0.8672  top5_acc: 1.0000  loss_cls: 0.1456
2025/08/08 12:18:50 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:18:50 - mmengine - INFO - Epoch(train)  [21][28/28]  base_lr: 9.3974e-03 lr: 9.3974e-03  eta: 2:23:20  time: 2.0559  data_time: 1.3635  memory: 30731  grad_norm: 3.6853  loss: 0.1433  top1_acc: 0.8704  top5_acc: 1.0000  loss_cls: 0.1433
2025/08/08 12:18:50 - mmengine - INFO - Saving checkpoint at 21 epochs
2025/08/08 12:19:04 - mmengine - INFO - Epoch(val) [21][10/10]    acc/top1: 0.8410  acc/top5: 1.0000  acc/mean1: 0.8427  data_time: 0.7453  time: 0.8264
2025/08/08 12:20:18 - mmengine - INFO - Epoch(train)  [22][20/28]  base_lr: 9.3163e-03 lr: 9.3163e-03  eta: 2:21:50  time: 3.7131  data_time: 2.8369  memory: 30731  grad_norm: 3.6555  loss: 0.1339  top1_acc: 0.8438  top5_acc: 1.0000  loss_cls: 0.1339
2025/08/08 12:20:27 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:20:27 - mmengine - INFO - Epoch(train)  [22][28/28]  base_lr: 9.3163e-03 lr: 9.3163e-03  eta: 2:19:59  time: 2.6100  data_time: 1.7960  memory: 30731  grad_norm: 3.8150  loss: 0.1394  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.1394
2025/08/08 12:20:27 - mmengine - INFO - Saving checkpoint at 22 epochs
2025/08/08 12:20:41 - mmengine - INFO - Epoch(val) [22][10/10]    acc/top1: 0.8467  acc/top5: 1.0000  acc/mean1: 0.8522  data_time: 0.8186  time: 0.9001
2025/08/08 12:21:58 - mmengine - INFO - Epoch(train)  [23][20/28]  base_lr: 9.2305e-03 lr: 9.2305e-03  eta: 2:18:40  time: 3.8029  data_time: 2.9823  memory: 30731  grad_norm: 3.5353  loss: 0.1446  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.1446
2025/08/08 12:22:06 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:22:06 - mmengine - INFO - Epoch(train)  [23][28/28]  base_lr: 9.2305e-03 lr: 9.2305e-03  eta: 2:16:55  time: 2.7479  data_time: 1.9736  memory: 30731  grad_norm: 3.6276  loss: 0.1367  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.1367
2025/08/08 12:22:07 - mmengine - INFO - Saving checkpoint at 23 epochs
2025/08/08 12:22:20 - mmengine - INFO - Epoch(val) [23][10/10]    acc/top1: 0.8414  acc/top5: 1.0000  acc/mean1: 0.8432  data_time: 0.7631  time: 0.8441
2025/08/08 12:23:32 - mmengine - INFO - Epoch(train)  [24][20/28]  base_lr: 9.1400e-03 lr: 9.1400e-03  eta: 2:15:21  time: 3.5378  data_time: 2.7344  memory: 30731  grad_norm: 3.4135  loss: 0.1331  top1_acc: 0.8750  top5_acc: 1.0000  loss_cls: 0.1331
2025/08/08 12:23:42 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:23:42 - mmengine - INFO - Epoch(train)  [24][28/28]  base_lr: 9.1400e-03 lr: 9.1400e-03  eta: 2:13:47  time: 2.4694  data_time: 1.7687  memory: 30731  grad_norm: 3.6345  loss: 0.1247  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.1247
2025/08/08 12:23:42 - mmengine - INFO - Saving checkpoint at 24 epochs
2025/08/08 12:23:57 - mmengine - INFO - Epoch(val) [24][10/10]    acc/top1: 0.8414  acc/top5: 1.0000  acc/mean1: 0.8403  data_time: 0.7972  time: 0.8786
2025/08/08 12:25:05 - mmengine - INFO - Epoch(train)  [25][20/28]  base_lr: 9.0451e-03 lr: 9.0451e-03  eta: 2:12:09  time: 3.3975  data_time: 2.5611  memory: 30731  grad_norm: 3.3329  loss: 0.1371  top1_acc: 0.9141  top5_acc: 1.0000  loss_cls: 0.1371
2025/08/08 12:25:14 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:25:14 - mmengine - INFO - Epoch(train)  [25][28/28]  base_lr: 9.0451e-03 lr: 9.0451e-03  eta: 2:10:36  time: 2.3402  data_time: 1.5938  memory: 30731  grad_norm: 3.3135  loss: 0.1264  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.1264
2025/08/08 12:25:15 - mmengine - INFO - Saving checkpoint at 25 epochs
2025/08/08 12:25:29 - mmengine - INFO - Epoch(val) [25][10/10]    acc/top1: 0.8515  acc/top5: 1.0000  acc/mean1: 0.8525  data_time: 0.8577  time: 0.9391
2025/08/08 12:26:38 - mmengine - INFO - Epoch(train)  [26][20/28]  base_lr: 8.9457e-03 lr: 8.9457e-03  eta: 2:09:04  time: 3.4370  data_time: 2.6398  memory: 30731  grad_norm: 3.5245  loss: 0.1281  top1_acc: 0.8906  top5_acc: 1.0000  loss_cls: 0.1281
2025/08/08 12:26:47 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:26:47 - mmengine - INFO - Epoch(train)  [26][28/28]  base_lr: 8.9457e-03 lr: 8.9457e-03  eta: 2:07:35  time: 2.4563  data_time: 1.6935  memory: 30731  grad_norm: 3.5192  loss: 0.1280  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.1280
2025/08/08 12:26:47 - mmengine - INFO - Saving checkpoint at 26 epochs
2025/08/08 12:27:01 - mmengine - INFO - Epoch(val) [26][10/10]    acc/top1: 0.8564  acc/top5: 1.0000  acc/mean1: 0.8595  data_time: 0.8209  time: 0.9016
2025/08/08 12:28:11 - mmengine - INFO - Epoch(train)  [27][20/28]  base_lr: 8.8420e-03 lr: 8.8420e-03  eta: 2:06:08  time: 3.4469  data_time: 2.5954  memory: 30731  grad_norm: 3.5129  loss: 0.1162  top1_acc: 0.8594  top5_acc: 1.0000  loss_cls: 0.1162
2025/08/08 12:28:19 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:28:19 - mmengine - INFO - Epoch(train)  [27][28/28]  base_lr: 8.8420e-03 lr: 8.8420e-03  eta: 2:04:42  time: 2.4452  data_time: 1.6455  memory: 30731  grad_norm: 3.5511  loss: 0.1175  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.1175
2025/08/08 12:28:20 - mmengine - INFO - Saving checkpoint at 27 epochs
2025/08/08 12:28:33 - mmengine - INFO - Epoch(val) [27][10/10]    acc/top1: 0.8584  acc/top5: 1.0000  acc/mean1: 0.8629  data_time: 0.7163  time: 0.7972
2025/08/08 12:29:42 - mmengine - INFO - Epoch(train)  [28][20/28]  base_lr: 8.7341e-03 lr: 8.7341e-03  eta: 2:03:18  time: 3.4585  data_time: 2.7502  memory: 30731  grad_norm: 3.4754  loss: 0.1239  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.1239
2025/08/08 12:29:52 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:29:52 - mmengine - INFO - Epoch(train)  [28][28/28]  base_lr: 8.7341e-03 lr: 8.7341e-03  eta: 2:01:57  time: 2.5195  data_time: 1.8239  memory: 30731  grad_norm: 3.7043  loss: 0.1266  top1_acc: 0.7963  top5_acc: 1.0000  loss_cls: 0.1266
2025/08/08 12:29:52 - mmengine - INFO - Saving checkpoint at 28 epochs
2025/08/08 12:30:07 - mmengine - INFO - Epoch(val) [28][10/10]    acc/top1: 0.8738  acc/top5: 1.0000  acc/mean1: 0.8726  data_time: 0.8693  time: 0.9493
2025/08/08 12:30:07 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_20.pth is removed
2025/08/08 12:30:11 - mmengine - INFO - The best checkpoint with 0.8738 acc/top1 at 28 epoch is saved to best_acc_top1_epoch_28.pth.
2025/08/08 12:31:20 - mmengine - INFO - Epoch(train)  [29][20/28]  base_lr: 8.6221e-03 lr: 8.6221e-03  eta: 2:00:24  time: 3.2155  data_time: 2.4701  memory: 30731  grad_norm: 3.4106  loss: 0.1236  top1_acc: 0.9062  top5_acc: 1.0000  loss_cls: 0.1236
2025/08/08 12:31:26 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:31:26 - mmengine - INFO - Epoch(train)  [29][28/28]  base_lr: 8.6221e-03 lr: 8.6221e-03  eta: 1:58:58  time: 2.0212  data_time: 1.3439  memory: 30731  grad_norm: 3.4175  loss: 0.1211  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.1211
2025/08/08 12:31:26 - mmengine - INFO - Saving checkpoint at 29 epochs
2025/08/08 12:31:40 - mmengine - INFO - Epoch(val) [29][10/10]    acc/top1: 0.8726  acc/top5: 1.0000  acc/mean1: 0.8754  data_time: 0.7543  time: 0.8355
2025/08/08 12:32:49 - mmengine - INFO - Epoch(train)  [30][20/28]  base_lr: 8.5062e-03 lr: 8.5062e-03  eta: 1:57:39  time: 3.4291  data_time: 2.7552  memory: 30731  grad_norm: 3.3680  loss: 0.1178  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.1178
2025/08/08 12:32:58 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:32:58 - mmengine - INFO - Epoch(train)  [30][28/28]  base_lr: 8.5062e-03 lr: 8.5062e-03  eta: 1:56:25  time: 2.4621  data_time: 1.8037  memory: 30731  grad_norm: 3.3376  loss: 0.1135  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.1135
2025/08/08 12:32:58 - mmengine - INFO - Saving checkpoint at 30 epochs
2025/08/08 12:33:10 - mmengine - INFO - Epoch(val) [30][10/10]    acc/top1: 0.8686  acc/top5: 1.0000  acc/mean1: 0.8692  data_time: 0.5554  time: 0.6385
2025/08/08 12:34:12 - mmengine - INFO - Epoch(train)  [31][20/28]  base_lr: 8.3864e-03 lr: 8.3864e-03  eta: 1:54:52  time: 3.0828  data_time: 2.2675  memory: 30731  grad_norm: 3.3832  loss: 0.1047  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.1047
2025/08/08 12:34:20 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:34:20 - mmengine - INFO - Epoch(train)  [31][28/28]  base_lr: 8.3864e-03 lr: 8.3864e-03  eta: 1:53:37  time: 2.1014  data_time: 1.3649  memory: 30731  grad_norm: 3.6643  loss: 0.1032  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.1032
2025/08/08 12:34:20 - mmengine - INFO - Saving checkpoint at 31 epochs
2025/08/08 12:34:32 - mmengine - INFO - Epoch(val) [31][10/10]    acc/top1: 0.8442  acc/top5: 1.0000  acc/mean1: 0.8437  data_time: 0.5922  time: 0.6720
2025/08/08 12:35:34 - mmengine - INFO - Epoch(train)  [32][20/28]  base_lr: 8.2629e-03 lr: 8.2629e-03  eta: 1:52:09  time: 3.1194  data_time: 2.1598  memory: 30731  grad_norm: 3.2032  loss: 0.1084  top1_acc: 0.9141  top5_acc: 1.0000  loss_cls: 0.1084
2025/08/08 12:35:41 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:35:41 - mmengine - INFO - Epoch(train)  [32][28/28]  base_lr: 8.2629e-03 lr: 8.2629e-03  eta: 1:50:56  time: 2.0869  data_time: 1.2350  memory: 30731  grad_norm: 3.4216  loss: 0.1131  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.1131
2025/08/08 12:35:41 - mmengine - INFO - Saving checkpoint at 32 epochs
2025/08/08 12:35:53 - mmengine - INFO - Epoch(val) [32][10/10]    acc/top1: 0.8710  acc/top5: 1.0000  acc/mean1: 0.8745  data_time: 0.5950  time: 0.6751
2025/08/08 12:36:54 - mmengine - INFO - Epoch(train)  [33][20/28]  base_lr: 8.1359e-03 lr: 8.1359e-03  eta: 1:49:29  time: 3.0829  data_time: 2.2733  memory: 30731  grad_norm: 3.6193  loss: 0.1142  top1_acc: 0.8516  top5_acc: 1.0000  loss_cls: 0.1142
2025/08/08 12:37:02 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:37:02 - mmengine - INFO - Epoch(train)  [33][28/28]  base_lr: 8.1359e-03 lr: 8.1359e-03  eta: 1:48:20  time: 2.0847  data_time: 1.3609  memory: 30731  grad_norm: 3.5691  loss: 0.1113  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.1113
2025/08/08 12:37:02 - mmengine - INFO - Saving checkpoint at 33 epochs
2025/08/08 12:37:14 - mmengine - INFO - Epoch(val) [33][10/10]    acc/top1: 0.8353  acc/top5: 1.0000  acc/mean1: 0.8444  data_time: 0.5503  time: 0.6292
2025/08/08 12:38:16 - mmengine - INFO - Epoch(train)  [34][20/28]  base_lr: 8.0054e-03 lr: 8.0054e-03  eta: 1:46:56  time: 3.1042  data_time: 2.2866  memory: 30731  grad_norm: 3.3795  loss: 0.1189  top1_acc: 0.9141  top5_acc: 1.0000  loss_cls: 0.1189
2025/08/08 12:38:23 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:38:23 - mmengine - INFO - Epoch(train)  [34][28/28]  base_lr: 8.0054e-03 lr: 8.0054e-03  eta: 1:45:49  time: 2.0772  data_time: 1.3555  memory: 30731  grad_norm: 3.5043  loss: 0.1134  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.1134
2025/08/08 12:38:23 - mmengine - INFO - Saving checkpoint at 34 epochs
2025/08/08 12:38:35 - mmengine - INFO - Epoch(val) [34][10/10]    acc/top1: 0.8158  acc/top5: 1.0000  acc/mean1: 0.8253  data_time: 0.6148  time: 0.6971
2025/08/08 12:39:37 - mmengine - INFO - Epoch(train)  [35][20/28]  base_lr: 7.8716e-03 lr: 7.8716e-03  eta: 1:44:28  time: 3.0999  data_time: 2.1684  memory: 30731  grad_norm: 3.2490  loss: 0.1017  top1_acc: 0.9062  top5_acc: 1.0000  loss_cls: 0.1017
2025/08/08 12:39:45 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:39:45 - mmengine - INFO - Epoch(train)  [35][28/28]  base_lr: 7.8716e-03 lr: 7.8716e-03  eta: 1:43:23  time: 2.0554  data_time: 1.2205  memory: 30731  grad_norm: 3.2384  loss: 0.1029  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.1029
2025/08/08 12:39:45 - mmengine - INFO - Saving checkpoint at 35 epochs
2025/08/08 12:39:57 - mmengine - INFO - Epoch(val) [35][10/10]    acc/top1: 0.8682  acc/top5: 1.0000  acc/mean1: 0.8723  data_time: 0.5399  time: 0.6194
2025/08/08 12:40:58 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:40:58 - mmengine - INFO - Epoch(train)  [36][20/28]  base_lr: 7.7347e-03 lr: 7.7347e-03  eta: 1:42:03  time: 3.0742  data_time: 2.1971  memory: 30731  grad_norm: 3.3206  loss: 0.1036  top1_acc: 0.9297  top5_acc: 1.0000  loss_cls: 0.1036
2025/08/08 12:41:06 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:41:06 - mmengine - INFO - Epoch(train)  [36][28/28]  base_lr: 7.7347e-03 lr: 7.7347e-03  eta: 1:41:01  time: 2.0649  data_time: 1.2614  memory: 30731  grad_norm: 3.5666  loss: 0.0989  top1_acc: 0.8333  top5_acc: 1.0000  loss_cls: 0.0989
2025/08/08 12:41:06 - mmengine - INFO - Saving checkpoint at 36 epochs
2025/08/08 12:41:18 - mmengine - INFO - Epoch(val) [36][10/10]    acc/top1: 0.8726  acc/top5: 1.0000  acc/mean1: 0.8739  data_time: 0.6190  time: 0.6986
2025/08/08 12:42:20 - mmengine - INFO - Epoch(train)  [37][20/28]  base_lr: 7.5948e-03 lr: 7.5948e-03  eta: 1:39:44  time: 3.1069  data_time: 2.2917  memory: 30731  grad_norm: 3.3033  loss: 0.1015  top1_acc: 0.8906  top5_acc: 1.0000  loss_cls: 0.1015
2025/08/08 12:42:27 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:42:27 - mmengine - INFO - Epoch(train)  [37][28/28]  base_lr: 7.5948e-03 lr: 7.5948e-03  eta: 1:38:43  time: 2.0815  data_time: 1.3428  memory: 30731  grad_norm: 3.4329  loss: 0.1048  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.1048
2025/08/08 12:42:27 - mmengine - INFO - Saving checkpoint at 37 epochs
2025/08/08 12:42:39 - mmengine - INFO - Epoch(val) [37][10/10]    acc/top1: 0.8560  acc/top5: 1.0000  acc/mean1: 0.8550  data_time: 0.5264  time: 0.6054
2025/08/08 12:43:41 - mmengine - INFO - Epoch(train)  [38][20/28]  base_lr: 7.4521e-03 lr: 7.4521e-03  eta: 1:37:27  time: 3.0961  data_time: 2.2289  memory: 30731  grad_norm: 3.1244  loss: 0.1022  top1_acc: 0.9062  top5_acc: 1.0000  loss_cls: 0.1022
2025/08/08 12:43:48 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:43:48 - mmengine - INFO - Epoch(train)  [38][28/28]  base_lr: 7.4521e-03 lr: 7.4521e-03  eta: 1:36:28  time: 2.0950  data_time: 1.2963  memory: 30731  grad_norm: 3.1837  loss: 0.1089  top1_acc: 0.7778  top5_acc: 1.0000  loss_cls: 0.1089
2025/08/08 12:43:48 - mmengine - INFO - Saving checkpoint at 38 epochs
2025/08/08 12:44:00 - mmengine - INFO - Epoch(val) [38][10/10]    acc/top1: 0.8548  acc/top5: 1.0000  acc/mean1: 0.8620  data_time: 0.6017  time: 0.6832
2025/08/08 12:45:03 - mmengine - INFO - Epoch(train)  [39][20/28]  base_lr: 7.3067e-03 lr: 7.3067e-03  eta: 1:35:15  time: 3.1136  data_time: 2.2710  memory: 30731  grad_norm: 3.3320  loss: 0.0912  top1_acc: 0.9375  top5_acc: 1.0000  loss_cls: 0.0912
2025/08/08 12:45:09 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:45:09 - mmengine - INFO - Epoch(train)  [39][28/28]  base_lr: 7.3067e-03 lr: 7.3067e-03  eta: 1:34:17  time: 2.1207  data_time: 1.3410  memory: 30731  grad_norm: 3.5225  loss: 0.1063  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.1063
2025/08/08 12:45:10 - mmengine - INFO - Saving checkpoint at 39 epochs
2025/08/08 12:45:21 - mmengine - INFO - Epoch(val) [39][10/10]    acc/top1: 0.8491  acc/top5: 1.0000  acc/mean1: 0.8525  data_time: 0.5682  time: 0.6480
2025/08/08 12:46:24 - mmengine - INFO - Epoch(train)  [40][20/28]  base_lr: 7.1588e-03 lr: 7.1588e-03  eta: 1:33:05  time: 3.1245  data_time: 2.2544  memory: 30731  grad_norm: 2.9721  loss: 0.0928  top1_acc: 0.9453  top5_acc: 1.0000  loss_cls: 0.0928
2025/08/08 12:46:30 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:46:30 - mmengine - INFO - Epoch(train)  [40][28/28]  base_lr: 7.1588e-03 lr: 7.1588e-03  eta: 1:32:09  time: 2.0870  data_time: 1.2804  memory: 30731  grad_norm: 3.0879  loss: 0.0963  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.0963
2025/08/08 12:46:30 - mmengine - INFO - Saving checkpoint at 40 epochs
2025/08/08 12:46:42 - mmengine - INFO - Epoch(val) [40][10/10]    acc/top1: 0.8714  acc/top5: 1.0000  acc/mean1: 0.8719  data_time: 0.5640  time: 0.6436
2025/08/08 12:47:44 - mmengine - INFO - Epoch(train)  [41][20/28]  base_lr: 7.0085e-03 lr: 7.0085e-03  eta: 1:30:57  time: 3.0981  data_time: 2.3331  memory: 30731  grad_norm: 3.2105  loss: 0.0853  top1_acc: 0.8828  top5_acc: 1.0000  loss_cls: 0.0853
2025/08/08 12:47:51 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:47:51 - mmengine - INFO - Epoch(train)  [41][28/28]  base_lr: 7.0085e-03 lr: 7.0085e-03  eta: 1:30:04  time: 2.0804  data_time: 1.3882  memory: 30731  grad_norm: 3.2806  loss: 0.0809  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0809
2025/08/08 12:47:52 - mmengine - INFO - Saving checkpoint at 41 epochs
2025/08/08 12:48:03 - mmengine - INFO - Epoch(val) [41][10/10]    acc/top1: 0.8394  acc/top5: 1.0000  acc/mean1: 0.8449  data_time: 0.5486  time: 0.6291
2025/08/08 12:49:05 - mmengine - INFO - Epoch(train)  [42][20/28]  base_lr: 6.8560e-03 lr: 6.8560e-03  eta: 1:28:53  time: 3.0783  data_time: 2.2648  memory: 30731  grad_norm: 3.0522  loss: 0.0864  top1_acc: 0.9453  top5_acc: 1.0000  loss_cls: 0.0864
2025/08/08 12:49:13 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:49:13 - mmengine - INFO - Epoch(train)  [42][28/28]  base_lr: 6.8560e-03 lr: 6.8560e-03  eta: 1:28:01  time: 2.1039  data_time: 1.3511  memory: 30731  grad_norm: 3.3047  loss: 0.0811  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.0811
2025/08/08 12:49:13 - mmengine - INFO - Saving checkpoint at 42 epochs
2025/08/08 12:49:25 - mmengine - INFO - Epoch(val) [42][10/10]    acc/top1: 0.8544  acc/top5: 1.0000  acc/mean1: 0.8521  data_time: 0.5887  time: 0.6695
2025/08/08 12:50:27 - mmengine - INFO - Epoch(train)  [43][20/28]  base_lr: 6.7015e-03 lr: 6.7015e-03  eta: 1:26:53  time: 3.1167  data_time: 2.2191  memory: 30731  grad_norm: 3.2190  loss: 0.0926  top1_acc: 0.8984  top5_acc: 1.0000  loss_cls: 0.0926
2025/08/08 12:50:34 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:50:34 - mmengine - INFO - Epoch(train)  [43][28/28]  base_lr: 6.7015e-03 lr: 6.7015e-03  eta: 1:26:01  time: 2.0461  data_time: 1.2830  memory: 30731  grad_norm: 3.4616  loss: 0.0949  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.0949
2025/08/08 12:50:34 - mmengine - INFO - Saving checkpoint at 43 epochs
2025/08/08 12:50:46 - mmengine - INFO - Epoch(val) [43][10/10]    acc/top1: 0.8661  acc/top5: 1.0000  acc/mean1: 0.8697  data_time: 0.6015  time: 0.6899
2025/08/08 12:51:48 - mmengine - INFO - Epoch(train)  [44][20/28]  base_lr: 6.5451e-03 lr: 6.5451e-03  eta: 1:24:53  time: 3.0856  data_time: 2.2217  memory: 30731  grad_norm: 3.1840  loss: 0.0728  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.0728
2025/08/08 12:51:55 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:51:55 - mmengine - INFO - Epoch(train)  [44][28/28]  base_lr: 6.5451e-03 lr: 6.5451e-03  eta: 1:24:03  time: 2.1007  data_time: 1.3182  memory: 30731  grad_norm: 3.3822  loss: 0.0844  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0844
2025/08/08 12:51:55 - mmengine - INFO - Saving checkpoint at 44 epochs
2025/08/08 12:52:07 - mmengine - INFO - Epoch(val) [44][10/10]    acc/top1: 0.8588  acc/top5: 1.0000  acc/mean1: 0.8638  data_time: 0.6264  time: 0.7065
2025/08/08 12:53:08 - mmengine - INFO - Epoch(train)  [45][20/28]  base_lr: 6.3870e-03 lr: 6.3870e-03  eta: 1:22:55  time: 3.0595  data_time: 2.2339  memory: 30731  grad_norm: 3.0499  loss: 0.0922  top1_acc: 0.8594  top5_acc: 1.0000  loss_cls: 0.0922
2025/08/08 12:53:16 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:53:16 - mmengine - INFO - Epoch(train)  [45][28/28]  base_lr: 6.3870e-03 lr: 6.3870e-03  eta: 1:22:08  time: 2.0738  data_time: 1.3332  memory: 30731  grad_norm: 3.2174  loss: 0.0850  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.0850
2025/08/08 12:53:16 - mmengine - INFO - Saving checkpoint at 45 epochs
2025/08/08 12:53:28 - mmengine - INFO - Epoch(val) [45][10/10]    acc/top1: 0.8690  acc/top5: 1.0000  acc/mean1: 0.8703  data_time: 0.5335  time: 0.6129
2025/08/08 12:54:30 - mmengine - INFO - Epoch(train)  [46][20/28]  base_lr: 6.2274e-03 lr: 6.2274e-03  eta: 1:21:02  time: 3.1163  data_time: 2.1830  memory: 30731  grad_norm: 3.0654  loss: 0.0837  top1_acc: 0.9375  top5_acc: 1.0000  loss_cls: 0.0837
2025/08/08 12:54:37 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:54:37 - mmengine - INFO - Epoch(train)  [46][28/28]  base_lr: 6.2274e-03 lr: 6.2274e-03  eta: 1:20:14  time: 2.0472  data_time: 1.2359  memory: 30731  grad_norm: 3.3301  loss: 0.0842  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0842
2025/08/08 12:54:37 - mmengine - INFO - Saving checkpoint at 46 epochs
2025/08/08 12:54:49 - mmengine - INFO - Epoch(val) [46][10/10]    acc/top1: 0.8763  acc/top5: 1.0000  acc/mean1: 0.8773  data_time: 0.6295  time: 0.7096
2025/08/08 12:54:49 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_28.pth is removed
2025/08/08 12:54:54 - mmengine - INFO - The best checkpoint with 0.8763 acc/top1 at 46 epoch is saved to best_acc_top1_epoch_46.pth.
2025/08/08 12:55:55 - mmengine - INFO - Epoch(train)  [47][20/28]  base_lr: 6.0665e-03 lr: 6.0665e-03  eta: 1:19:02  time: 2.8282  data_time: 2.0275  memory: 30731  grad_norm: 3.0190  loss: 0.0807  top1_acc: 0.9531  top5_acc: 1.0000  loss_cls: 0.0807
2025/08/08 12:56:01 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:56:01 - mmengine - INFO - Epoch(train)  [47][28/28]  base_lr: 6.0665e-03 lr: 6.0665e-03  eta: 1:18:15  time: 1.7331  data_time: 1.0138  memory: 30731  grad_norm: 3.0245  loss: 0.0762  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.0762
2025/08/08 12:56:01 - mmengine - INFO - Saving checkpoint at 47 epochs
2025/08/08 12:56:13 - mmengine - INFO - Epoch(val) [47][10/10]    acc/top1: 0.8637  acc/top5: 1.0000  acc/mean1: 0.8640  data_time: 0.6133  time: 0.6936
2025/08/08 12:57:15 - mmengine - INFO - Epoch(train)  [48][20/28]  base_lr: 5.9044e-03 lr: 5.9044e-03  eta: 1:17:10  time: 3.0906  data_time: 2.2822  memory: 30731  grad_norm: 3.0614  loss: 0.0698  top1_acc: 0.9609  top5_acc: 1.0000  loss_cls: 0.0698
2025/08/08 12:57:23 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:57:23 - mmengine - INFO - Epoch(train)  [48][28/28]  base_lr: 5.9044e-03 lr: 5.9044e-03  eta: 1:16:26  time: 2.1030  data_time: 1.3759  memory: 30731  grad_norm: 3.2307  loss: 0.0716  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.0716
2025/08/08 12:57:23 - mmengine - INFO - Saving checkpoint at 48 epochs
2025/08/08 12:57:35 - mmengine - INFO - Epoch(val) [48][10/10]    acc/top1: 0.8592  acc/top5: 1.0000  acc/mean1: 0.8629  data_time: 0.5720  time: 0.6539
2025/08/08 12:58:36 - mmengine - INFO - Epoch(train)  [49][20/28]  base_lr: 5.7413e-03 lr: 5.7413e-03  eta: 1:15:21  time: 3.0607  data_time: 2.2447  memory: 30731  grad_norm: 3.2794  loss: 0.0701  top1_acc: 0.9297  top5_acc: 1.0000  loss_cls: 0.0701
2025/08/08 12:58:43 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 12:58:43 - mmengine - INFO - Epoch(train)  [49][28/28]  base_lr: 5.7413e-03 lr: 5.7413e-03  eta: 1:14:37  time: 2.0789  data_time: 1.3178  memory: 30731  grad_norm: 3.5150  loss: 0.0674  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.0674
2025/08/08 12:58:43 - mmengine - INFO - Saving checkpoint at 49 epochs
2025/08/08 12:58:56 - mmengine - INFO - Epoch(val) [49][10/10]    acc/top1: 0.8653  acc/top5: 1.0000  acc/mean1: 0.8658  data_time: 0.6286  time: 0.7073
2025/08/08 12:59:57 - mmengine - INFO - Epoch(train)  [50][20/28]  base_lr: 5.5774e-03 lr: 5.5774e-03  eta: 1:13:33  time: 3.0619  data_time: 2.3319  memory: 30731  grad_norm: 2.9423  loss: 0.0765  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.0765
2025/08/08 13:00:05 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:00:05 - mmengine - INFO - Epoch(train)  [50][28/28]  base_lr: 5.5774e-03 lr: 5.5774e-03  eta: 1:12:51  time: 2.1396  data_time: 1.4269  memory: 30731  grad_norm: 3.1946  loss: 0.0702  top1_acc: 0.8704  top5_acc: 1.0000  loss_cls: 0.0702
2025/08/08 13:00:05 - mmengine - INFO - Saving checkpoint at 50 epochs
2025/08/08 13:00:17 - mmengine - INFO - Epoch(val) [50][10/10]    acc/top1: 0.8609  acc/top5: 1.0000  acc/mean1: 0.8651  data_time: 0.5188  time: 0.5983
2025/08/08 13:01:18 - mmengine - INFO - Epoch(train)  [51][20/28]  base_lr: 5.4129e-03 lr: 5.4129e-03  eta: 1:11:47  time: 3.0750  data_time: 2.2244  memory: 30731  grad_norm: 3.0503  loss: 0.0646  top1_acc: 0.9766  top5_acc: 1.0000  loss_cls: 0.0646
2025/08/08 13:01:26 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:01:26 - mmengine - INFO - Epoch(train)  [51][28/28]  base_lr: 5.4129e-03 lr: 5.4129e-03  eta: 1:11:05  time: 2.0688  data_time: 1.2876  memory: 30731  grad_norm: 3.5354  loss: 0.0768  top1_acc: 0.8519  top5_acc: 1.0000  loss_cls: 0.0768
2025/08/08 13:01:26 - mmengine - INFO - Saving checkpoint at 51 epochs
2025/08/08 13:01:38 - mmengine - INFO - Epoch(val) [51][10/10]    acc/top1: 0.8531  acc/top5: 1.0000  acc/mean1: 0.8525  data_time: 0.6104  time: 0.6907
2025/08/08 13:02:39 - mmengine - INFO - Epoch(train)  [52][20/28]  base_lr: 5.2479e-03 lr: 5.2479e-03  eta: 1:10:02  time: 3.0721  data_time: 2.2891  memory: 30731  grad_norm: 3.0325  loss: 0.0688  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0688
2025/08/08 13:02:46 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:02:46 - mmengine - INFO - Epoch(train)  [52][28/28]  base_lr: 5.2479e-03 lr: 5.2479e-03  eta: 1:09:21  time: 2.0950  data_time: 1.3566  memory: 30731  grad_norm: 3.0333  loss: 0.0778  top1_acc: 0.8519  top5_acc: 1.0000  loss_cls: 0.0778
2025/08/08 13:02:47 - mmengine - INFO - Saving checkpoint at 52 epochs
2025/08/08 13:02:58 - mmengine - INFO - Epoch(val) [52][10/10]    acc/top1: 0.8718  acc/top5: 1.0000  acc/mean1: 0.8747  data_time: 0.5272  time: 0.6063
2025/08/08 13:04:00 - mmengine - INFO - Epoch(train)  [53][20/28]  base_lr: 5.0827e-03 lr: 5.0827e-03  eta: 1:08:19  time: 3.0888  data_time: 2.3153  memory: 30731  grad_norm: 2.8707  loss: 0.0502  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0502
2025/08/08 13:04:07 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:04:07 - mmengine - INFO - Epoch(train)  [53][28/28]  base_lr: 5.0827e-03 lr: 5.0827e-03  eta: 1:07:39  time: 2.0807  data_time: 1.3743  memory: 30731  grad_norm: 3.3513  loss: 0.0618  top1_acc: 0.8889  top5_acc: 1.0000  loss_cls: 0.0618
2025/08/08 13:04:07 - mmengine - INFO - Saving checkpoint at 53 epochs
2025/08/08 13:04:19 - mmengine - INFO - Epoch(val) [53][10/10]    acc/top1: 0.8734  acc/top5: 1.0000  acc/mean1: 0.8750  data_time: 0.6004  time: 0.6835
2025/08/08 13:05:21 - mmengine - INFO - Epoch(train)  [54][20/28]  base_lr: 4.9173e-03 lr: 4.9173e-03  eta: 1:06:37  time: 3.0948  data_time: 2.3263  memory: 30731  grad_norm: 2.9508  loss: 0.0635  top1_acc: 0.9453  top5_acc: 1.0000  loss_cls: 0.0635
2025/08/08 13:05:28 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:05:28 - mmengine - INFO - Epoch(train)  [54][28/28]  base_lr: 4.9173e-03 lr: 4.9173e-03  eta: 1:05:58  time: 2.0898  data_time: 1.3916  memory: 30731  grad_norm: 3.1200  loss: 0.0568  top1_acc: 0.9259  top5_acc: 1.0000  loss_cls: 0.0568
2025/08/08 13:05:29 - mmengine - INFO - Saving checkpoint at 54 epochs
2025/08/08 13:05:40 - mmengine - INFO - Epoch(val) [54][10/10]    acc/top1: 0.8832  acc/top5: 1.0000  acc/mean1: 0.8847  data_time: 0.5659  time: 0.6452
2025/08/08 13:05:40 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_46.pth is removed
2025/08/08 13:05:44 - mmengine - INFO - The best checkpoint with 0.8832 acc/top1 at 54 epoch is saved to best_acc_top1_epoch_54.pth.
2025/08/08 13:06:46 - mmengine - INFO - Epoch(train)  [55][20/28]  base_lr: 4.7521e-03 lr: 4.7521e-03  eta: 1:04:53  time: 2.8628  data_time: 2.0069  memory: 30731  grad_norm: 2.9045  loss: 0.0565  top1_acc: 0.9297  top5_acc: 1.0000  loss_cls: 0.0565
2025/08/08 13:06:52 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:06:52 - mmengine - INFO - Epoch(train)  [55][28/28]  base_lr: 4.7521e-03 lr: 4.7521e-03  eta: 1:04:13  time: 1.7725  data_time: 0.9990  memory: 30731  grad_norm: 2.9807  loss: 0.0592  top1_acc: 0.9074  top5_acc: 1.0000  loss_cls: 0.0592
2025/08/08 13:06:52 - mmengine - INFO - Saving checkpoint at 55 epochs
2025/08/08 13:07:04 - mmengine - INFO - Epoch(val) [55][10/10]    acc/top1: 0.8734  acc/top5: 1.0000  acc/mean1: 0.8740  data_time: 0.6055  time: 0.6864
2025/08/08 13:08:06 - mmengine - INFO - Epoch(train)  [56][20/28]  base_lr: 4.5871e-03 lr: 4.5871e-03  eta: 1:03:13  time: 3.1006  data_time: 2.2436  memory: 30731  grad_norm: 3.1707  loss: 0.0556  top1_acc: 0.9609  top5_acc: 1.0000  loss_cls: 0.0556
2025/08/08 13:08:14 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:08:14 - mmengine - INFO - Epoch(train)  [56][28/28]  base_lr: 4.5871e-03 lr: 4.5871e-03  eta: 1:02:35  time: 2.1012  data_time: 1.2938  memory: 30731  grad_norm: 3.2895  loss: 0.0516  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0516
2025/08/08 13:08:14 - mmengine - INFO - Saving checkpoint at 56 epochs
2025/08/08 13:08:26 - mmengine - INFO - Epoch(val) [56][10/10]    acc/top1: 0.8738  acc/top5: 1.0000  acc/mean1: 0.8734  data_time: 0.5628  time: 0.6443
2025/08/08 13:09:27 - mmengine - INFO - Epoch(train)  [57][20/28]  base_lr: 4.4226e-03 lr: 4.4226e-03  eta: 1:01:34  time: 3.0720  data_time: 2.2056  memory: 30731  grad_norm: 3.2089  loss: 0.0544  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0544
2025/08/08 13:09:35 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:09:35 - mmengine - INFO - Epoch(train)  [57][28/28]  base_lr: 4.4226e-03 lr: 4.4226e-03  eta: 1:00:57  time: 2.0866  data_time: 1.2837  memory: 30731  grad_norm: 3.2242  loss: 0.0544  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0544
2025/08/08 13:09:35 - mmengine - INFO - Saving checkpoint at 57 epochs
2025/08/08 13:09:47 - mmengine - INFO - Epoch(val) [57][10/10]    acc/top1: 0.8799  acc/top5: 1.0000  acc/mean1: 0.8813  data_time: 0.6003  time: 0.6829
2025/08/08 13:10:49 - mmengine - INFO - Epoch(train)  [58][20/28]  base_lr: 4.2587e-03 lr: 4.2587e-03  eta: 0:59:57  time: 3.1217  data_time: 2.2554  memory: 30731  grad_norm: 2.9583  loss: 0.0559  top1_acc: 0.9219  top5_acc: 1.0000  loss_cls: 0.0559
2025/08/08 13:10:56 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:10:56 - mmengine - INFO - Epoch(train)  [58][28/28]  base_lr: 4.2587e-03 lr: 4.2587e-03  eta: 0:59:20  time: 2.0897  data_time: 1.3029  memory: 30731  grad_norm: 3.0379  loss: 0.0567  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0567
2025/08/08 13:10:56 - mmengine - INFO - Saving checkpoint at 58 epochs
2025/08/08 13:11:07 - mmengine - INFO - Epoch(val) [58][10/10]    acc/top1: 0.8779  acc/top5: 1.0000  acc/mean1: 0.8801  data_time: 0.5885  time: 0.6685
2025/08/08 13:12:10 - mmengine - INFO - Epoch(train)  [59][20/28]  base_lr: 4.0956e-03 lr: 4.0956e-03  eta: 0:58:21  time: 3.1296  data_time: 2.1768  memory: 30731  grad_norm: 2.7264  loss: 0.0477  top1_acc: 0.9453  top5_acc: 1.0000  loss_cls: 0.0477
2025/08/08 13:12:16 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:12:16 - mmengine - INFO - Epoch(train)  [59][28/28]  base_lr: 4.0956e-03 lr: 4.0956e-03  eta: 0:57:44  time: 2.0535  data_time: 1.1900  memory: 30731  grad_norm: 2.9340  loss: 0.0556  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0556
2025/08/08 13:12:16 - mmengine - INFO - Saving checkpoint at 59 epochs
2025/08/08 13:12:28 - mmengine - INFO - Epoch(val) [59][10/10]    acc/top1: 0.8767  acc/top5: 1.0000  acc/mean1: 0.8771  data_time: 0.5722  time: 0.6525
2025/08/08 13:13:30 - mmengine - INFO - Epoch(train)  [60][20/28]  base_lr: 3.9335e-03 lr: 3.9335e-03  eta: 0:56:45  time: 3.0862  data_time: 2.2399  memory: 30731  grad_norm: 2.7608  loss: 0.0461  top1_acc: 0.9688  top5_acc: 1.0000  loss_cls: 0.0461
2025/08/08 13:13:38 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:13:38 - mmengine - INFO - Epoch(train)  [60][28/28]  base_lr: 3.9335e-03 lr: 3.9335e-03  eta: 0:56:10  time: 2.0969  data_time: 1.2990  memory: 30731  grad_norm: 3.0035  loss: 0.0496  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0496
2025/08/08 13:13:38 - mmengine - INFO - Saving checkpoint at 60 epochs
2025/08/08 13:13:50 - mmengine - INFO - Epoch(val) [60][10/10]    acc/top1: 0.8759  acc/top5: 1.0000  acc/mean1: 0.8766  data_time: 0.5647  time: 0.6453
2025/08/08 13:14:51 - mmengine - INFO - Epoch(train)  [61][20/28]  base_lr: 3.7726e-03 lr: 3.7726e-03  eta: 0:55:10  time: 3.0595  data_time: 2.2817  memory: 30731  grad_norm: 3.1928  loss: 0.0514  top1_acc: 0.9531  top5_acc: 1.0000  loss_cls: 0.0514
2025/08/08 13:14:58 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:14:58 - mmengine - INFO - Epoch(train)  [61][28/28]  base_lr: 3.7726e-03 lr: 3.7726e-03  eta: 0:54:36  time: 2.1232  data_time: 1.3867  memory: 30731  grad_norm: 3.2105  loss: 0.0478  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0478
2025/08/08 13:14:58 - mmengine - INFO - Saving checkpoint at 61 epochs
2025/08/08 13:15:10 - mmengine - INFO - Epoch(val) [61][10/10]    acc/top1: 0.8787  acc/top5: 1.0000  acc/mean1: 0.8816  data_time: 0.5618  time: 0.6422
2025/08/08 13:16:12 - mmengine - INFO - Epoch(train)  [62][20/28]  base_lr: 3.6130e-03 lr: 3.6130e-03  eta: 0:53:37  time: 3.0917  data_time: 2.2748  memory: 30731  grad_norm: 2.5905  loss: 0.0403  top1_acc: 0.9609  top5_acc: 1.0000  loss_cls: 0.0403
2025/08/08 13:16:20 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:16:20 - mmengine - INFO - Epoch(train)  [62][28/28]  base_lr: 3.6130e-03 lr: 3.6130e-03  eta: 0:53:03  time: 2.0814  data_time: 1.3579  memory: 30731  grad_norm: 2.7968  loss: 0.0463  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0463
2025/08/08 13:16:20 - mmengine - INFO - Saving checkpoint at 62 epochs
2025/08/08 13:16:32 - mmengine - INFO - Epoch(val) [62][10/10]    acc/top1: 0.8807  acc/top5: 1.0000  acc/mean1: 0.8815  data_time: 0.6127  time: 0.6927
2025/08/08 13:17:33 - mmengine - INFO - Epoch(train)  [63][20/28]  base_lr: 3.4549e-03 lr: 3.4549e-03  eta: 0:52:04  time: 3.0842  data_time: 2.3302  memory: 30731  grad_norm: 2.6386  loss: 0.0335  top1_acc: 0.9844  top5_acc: 1.0000  loss_cls: 0.0335
2025/08/08 13:17:41 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:17:41 - mmengine - INFO - Epoch(train)  [63][28/28]  base_lr: 3.4549e-03 lr: 3.4549e-03  eta: 0:51:30  time: 2.1250  data_time: 1.3911  memory: 30731  grad_norm: 2.8868  loss: 0.0339  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0339
2025/08/08 13:17:41 - mmengine - INFO - Saving checkpoint at 63 epochs
2025/08/08 13:17:53 - mmengine - INFO - Epoch(val) [63][10/10]    acc/top1: 0.8775  acc/top5: 1.0000  acc/mean1: 0.8773  data_time: 0.5853  time: 0.6675
2025/08/08 13:18:55 - mmengine - INFO - Epoch(train)  [64][20/28]  base_lr: 3.2985e-03 lr: 3.2985e-03  eta: 0:50:32  time: 3.1097  data_time: 2.2959  memory: 30731  grad_norm: 2.9382  loss: 0.0432  top1_acc: 0.9297  top5_acc: 1.0000  loss_cls: 0.0432
2025/08/08 13:19:02 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:19:02 - mmengine - INFO - Epoch(train)  [64][28/28]  base_lr: 3.2985e-03 lr: 3.2985e-03  eta: 0:49:59  time: 2.0825  data_time: 1.3492  memory: 30731  grad_norm: 3.0184  loss: 0.0437  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0437
2025/08/08 13:19:02 - mmengine - INFO - Saving checkpoint at 64 epochs
2025/08/08 13:19:14 - mmengine - INFO - Epoch(val) [64][10/10]    acc/top1: 0.8880  acc/top5: 1.0000  acc/mean1: 0.8905  data_time: 0.5660  time: 0.6471
2025/08/08 13:19:14 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_54.pth is removed
2025/08/08 13:19:18 - mmengine - INFO - The best checkpoint with 0.8880 acc/top1 at 64 epoch is saved to best_acc_top1_epoch_64.pth.
2025/08/08 13:20:20 - mmengine - INFO - Epoch(train)  [65][20/28]  base_lr: 3.1440e-03 lr: 3.1440e-03  eta: 0:48:58  time: 2.8491  data_time: 2.1645  memory: 30731  grad_norm: 2.9152  loss: 0.0397  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0397
2025/08/08 13:20:25 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:20:25 - mmengine - INFO - Epoch(train)  [65][28/28]  base_lr: 3.1440e-03 lr: 3.1440e-03  eta: 0:48:24  time: 1.7898  data_time: 1.1460  memory: 30731  grad_norm: 2.8222  loss: 0.0404  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0404
2025/08/08 13:20:26 - mmengine - INFO - Saving checkpoint at 65 epochs
2025/08/08 13:20:38 - mmengine - INFO - Epoch(val) [65][10/10]    acc/top1: 0.8836  acc/top5: 1.0000  acc/mean1: 0.8854  data_time: 0.5607  time: 0.6493
2025/08/08 13:21:40 - mmengine - INFO - Epoch(train)  [66][20/28]  base_lr: 2.9915e-03 lr: 2.9915e-03  eta: 0:47:26  time: 3.1186  data_time: 2.1515  memory: 30731  grad_norm: 3.3522  loss: 0.0446  top1_acc: 0.9766  top5_acc: 1.0000  loss_cls: 0.0446
2025/08/08 13:21:47 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:21:47 - mmengine - INFO - Epoch(train)  [66][28/28]  base_lr: 2.9915e-03 lr: 2.9915e-03  eta: 0:46:54  time: 2.0418  data_time: 1.2107  memory: 30731  grad_norm: 3.1582  loss: 0.0480  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0480
2025/08/08 13:21:47 - mmengine - INFO - Saving checkpoint at 66 epochs
2025/08/08 13:21:59 - mmengine - INFO - Epoch(val) [66][10/10]    acc/top1: 0.8836  acc/top5: 1.0000  acc/mean1: 0.8847  data_time: 0.6133  time: 0.6927
2025/08/08 13:23:01 - mmengine - INFO - Epoch(train)  [67][20/28]  base_lr: 2.8412e-03 lr: 2.8412e-03  eta: 0:45:56  time: 3.0994  data_time: 2.2508  memory: 30731  grad_norm: 2.8948  loss: 0.0403  top1_acc: 0.9141  top5_acc: 1.0000  loss_cls: 0.0403
2025/08/08 13:23:08 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:23:08 - mmengine - INFO - Epoch(train)  [67][28/28]  base_lr: 2.8412e-03 lr: 2.8412e-03  eta: 0:45:25  time: 2.1022  data_time: 1.3335  memory: 30731  grad_norm: 2.9292  loss: 0.0346  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0346
2025/08/08 13:23:09 - mmengine - INFO - Saving checkpoint at 67 epochs
2025/08/08 13:23:20 - mmengine - INFO - Epoch(val) [67][10/10]    acc/top1: 0.8828  acc/top5: 1.0000  acc/mean1: 0.8823  data_time: 0.5558  time: 0.6382
2025/08/08 13:24:23 - mmengine - INFO - Epoch(train)  [68][20/28]  base_lr: 2.6933e-03 lr: 2.6933e-03  eta: 0:44:27  time: 3.1157  data_time: 2.2622  memory: 30731  grad_norm: 2.6626  loss: 0.0356  top1_acc: 0.9688  top5_acc: 1.0000  loss_cls: 0.0356
2025/08/08 13:24:30 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:24:30 - mmengine - INFO - Epoch(train)  [68][28/28]  base_lr: 2.6933e-03 lr: 2.6933e-03  eta: 0:43:56  time: 2.1354  data_time: 1.3196  memory: 30731  grad_norm: 2.7764  loss: 0.0342  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0342
2025/08/08 13:24:30 - mmengine - INFO - Saving checkpoint at 68 epochs
2025/08/08 13:24:42 - mmengine - INFO - Epoch(val) [68][10/10]    acc/top1: 0.8771  acc/top5: 1.0000  acc/mean1: 0.8778  data_time: 0.5719  time: 0.6620
2025/08/08 13:25:45 - mmengine - INFO - Epoch(train)  [69][20/28]  base_lr: 2.5479e-03 lr: 2.5479e-03  eta: 0:42:59  time: 3.1419  data_time: 2.3192  memory: 30731  grad_norm: 2.5175  loss: 0.0334  top1_acc: 0.9844  top5_acc: 1.0000  loss_cls: 0.0334
2025/08/08 13:25:51 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:25:51 - mmengine - INFO - Epoch(train)  [69][28/28]  base_lr: 2.5479e-03 lr: 2.5479e-03  eta: 0:42:28  time: 2.0917  data_time: 1.3483  memory: 30731  grad_norm: 2.7175  loss: 0.0337  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0337
2025/08/08 13:25:51 - mmengine - INFO - Saving checkpoint at 69 epochs
2025/08/08 13:26:03 - mmengine - INFO - Epoch(val) [69][10/10]    acc/top1: 0.8897  acc/top5: 1.0000  acc/mean1: 0.8905  data_time: 0.6024  time: 0.6816
2025/08/08 13:26:03 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_64.pth is removed
2025/08/08 13:26:07 - mmengine - INFO - The best checkpoint with 0.8897 acc/top1 at 69 epoch is saved to best_acc_top1_epoch_69.pth.
2025/08/08 13:27:09 - mmengine - INFO - Epoch(train)  [70][20/28]  base_lr: 2.4052e-03 lr: 2.4052e-03  eta: 0:41:28  time: 2.8213  data_time: 2.0415  memory: 30731  grad_norm: 2.6571  loss: 0.0267  top1_acc: 0.9688  top5_acc: 1.0000  loss_cls: 0.0267
2025/08/08 13:27:14 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:27:14 - mmengine - INFO - Epoch(train)  [70][28/28]  base_lr: 2.4052e-03 lr: 2.4052e-03  eta: 0:40:57  time: 1.7257  data_time: 1.0567  memory: 30731  grad_norm: 2.9526  loss: 0.0316  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0316
2025/08/08 13:27:14 - mmengine - INFO - Saving checkpoint at 70 epochs
2025/08/08 13:27:26 - mmengine - INFO - Epoch(val) [70][10/10]    acc/top1: 0.8824  acc/top5: 1.0000  acc/mean1: 0.8849  data_time: 0.6056  time: 0.6846
2025/08/08 13:28:28 - mmengine - INFO - Epoch(train)  [71][20/28]  base_lr: 2.2653e-03 lr: 2.2653e-03  eta: 0:40:00  time: 3.0905  data_time: 2.2950  memory: 30731  grad_norm: 2.6401  loss: 0.0300  top1_acc: 0.9531  top5_acc: 1.0000  loss_cls: 0.0300
2025/08/08 13:28:36 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:28:36 - mmengine - INFO - Epoch(train)  [71][28/28]  base_lr: 2.2653e-03 lr: 2.2653e-03  eta: 0:39:30  time: 2.1234  data_time: 1.3904  memory: 30731  grad_norm: 2.5926  loss: 0.0270  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0270
2025/08/08 13:28:36 - mmengine - INFO - Saving checkpoint at 71 epochs
2025/08/08 13:28:48 - mmengine - INFO - Epoch(val) [71][10/10]    acc/top1: 0.8856  acc/top5: 1.0000  acc/mean1: 0.8857  data_time: 0.6083  time: 0.6889
2025/08/08 13:29:32 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:29:52 - mmengine - INFO - Epoch(train)  [72][20/28]  base_lr: 2.1284e-03 lr: 2.1284e-03  eta: 0:38:34  time: 3.2070  data_time: 2.2907  memory: 30731  grad_norm: 2.6797  loss: 0.0275  top1_acc: 0.9688  top5_acc: 1.0000  loss_cls: 0.0275
2025/08/08 13:29:59 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:29:59 - mmengine - INFO - Epoch(train)  [72][28/28]  base_lr: 2.1284e-03 lr: 2.1284e-03  eta: 0:38:04  time: 2.1323  data_time: 1.2932  memory: 30731  grad_norm: 2.7722  loss: 0.0296  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0296
2025/08/08 13:29:59 - mmengine - INFO - Saving checkpoint at 72 epochs
2025/08/08 13:30:11 - mmengine - INFO - Epoch(val) [72][10/10]    acc/top1: 0.8888  acc/top5: 1.0000  acc/mean1: 0.8907  data_time: 0.5701  time: 0.6523
2025/08/08 13:31:13 - mmengine - INFO - Epoch(train)  [73][20/28]  base_lr: 1.9946e-03 lr: 1.9946e-03  eta: 0:37:07  time: 3.1256  data_time: 2.2675  memory: 30731  grad_norm: 2.5292  loss: 0.0268  top1_acc: 0.9375  top5_acc: 1.0000  loss_cls: 0.0268
2025/08/08 13:31:20 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:31:20 - mmengine - INFO - Epoch(train)  [73][28/28]  base_lr: 1.9946e-03 lr: 1.9946e-03  eta: 0:36:38  time: 2.0941  data_time: 1.3112  memory: 30731  grad_norm: 2.9731  loss: 0.0366  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0366
2025/08/08 13:31:21 - mmengine - INFO - Saving checkpoint at 73 epochs
2025/08/08 13:31:32 - mmengine - INFO - Epoch(val) [73][10/10]    acc/top1: 0.8864  acc/top5: 1.0000  acc/mean1: 0.8868  data_time: 0.5675  time: 0.6519
2025/08/08 13:32:35 - mmengine - INFO - Epoch(train)  [74][20/28]  base_lr: 1.8641e-03 lr: 1.8641e-03  eta: 0:35:41  time: 3.1253  data_time: 2.2364  memory: 30731  grad_norm: 2.4369  loss: 0.0253  top1_acc: 0.9766  top5_acc: 1.0000  loss_cls: 0.0253
2025/08/08 13:32:42 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:32:42 - mmengine - INFO - Epoch(train)  [74][28/28]  base_lr: 1.8641e-03 lr: 1.8641e-03  eta: 0:35:12  time: 2.0912  data_time: 1.2790  memory: 30731  grad_norm: 2.7079  loss: 0.0246  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0246
2025/08/08 13:32:42 - mmengine - INFO - Saving checkpoint at 74 epochs
2025/08/08 13:32:54 - mmengine - INFO - Epoch(val) [74][10/10]    acc/top1: 0.8901  acc/top5: 1.0000  acc/mean1: 0.8907  data_time: 0.5189  time: 0.6000
2025/08/08 13:32:54 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_69.pth is removed
2025/08/08 13:32:58 - mmengine - INFO - The best checkpoint with 0.8901 acc/top1 at 74 epoch is saved to best_acc_top1_epoch_74.pth.
2025/08/08 13:34:00 - mmengine - INFO - Epoch(train)  [75][20/28]  base_lr: 1.7371e-03 lr: 1.7371e-03  eta: 0:34:13  time: 2.8242  data_time: 2.0712  memory: 30731  grad_norm: 2.3183  loss: 0.0192  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0192
2025/08/08 13:34:05 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:34:05 - mmengine - INFO - Epoch(train)  [75][28/28]  base_lr: 1.7371e-03 lr: 1.7371e-03  eta: 0:33:44  time: 1.7621  data_time: 1.0549  memory: 30731  grad_norm: 2.3627  loss: 0.0181  top1_acc: 0.9444  top5_acc: 1.0000  loss_cls: 0.0181
2025/08/08 13:34:05 - mmengine - INFO - Saving checkpoint at 75 epochs
2025/08/08 13:34:17 - mmengine - INFO - Epoch(val) [75][10/10]    acc/top1: 0.8836  acc/top5: 1.0000  acc/mean1: 0.8829  data_time: 0.5846  time: 0.6653
2025/08/08 13:35:19 - mmengine - INFO - Epoch(train)  [76][20/28]  base_lr: 1.6136e-03 lr: 1.6136e-03  eta: 0:32:48  time: 3.0858  data_time: 2.2498  memory: 30731  grad_norm: 2.0069  loss: 0.0189  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0189
2025/08/08 13:35:27 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:35:27 - mmengine - INFO - Epoch(train)  [76][28/28]  base_lr: 1.6136e-03 lr: 1.6136e-03  eta: 0:32:20  time: 2.0666  data_time: 1.3214  memory: 30731  grad_norm: 2.1027  loss: 0.0216  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0216
2025/08/08 13:35:27 - mmengine - INFO - Saving checkpoint at 76 epochs
2025/08/08 13:35:39 - mmengine - INFO - Epoch(val) [76][10/10]    acc/top1: 0.8876  acc/top5: 1.0000  acc/mean1: 0.8890  data_time: 0.6186  time: 0.7001
2025/08/08 13:36:41 - mmengine - INFO - Epoch(train)  [77][20/28]  base_lr: 1.4938e-03 lr: 1.4938e-03  eta: 0:31:23  time: 3.0983  data_time: 2.2785  memory: 30731  grad_norm: 2.3651  loss: 0.0143  top1_acc: 0.9844  top5_acc: 1.0000  loss_cls: 0.0143
2025/08/08 13:36:48 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:36:48 - mmengine - INFO - Epoch(train)  [77][28/28]  base_lr: 1.4938e-03 lr: 1.4938e-03  eta: 0:30:55  time: 2.1104  data_time: 1.3280  memory: 30731  grad_norm: 2.7056  loss: 0.0150  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0150
2025/08/08 13:36:48 - mmengine - INFO - Saving checkpoint at 77 epochs
2025/08/08 13:37:00 - mmengine - INFO - Epoch(val) [77][10/10]    acc/top1: 0.8929  acc/top5: 1.0000  acc/mean1: 0.8934  data_time: 0.5459  time: 0.6259
2025/08/08 13:37:00 - mmengine - INFO - The previous best checkpoint /root/share175/sport_trains/sit_up/classify_cheating/mmaction2_main0808/work_dirs/PoseRgb_focal/best_acc_top1_epoch_74.pth is removed
2025/08/08 13:37:04 - mmengine - INFO - The best checkpoint with 0.8929 acc/top1 at 77 epoch is saved to best_acc_top1_epoch_77.pth.
2025/08/08 13:38:06 - mmengine - INFO - Epoch(train)  [78][20/28]  base_lr: 1.3779e-03 lr: 1.3779e-03  eta: 0:29:58  time: 2.8667  data_time: 2.1188  memory: 30731  grad_norm: 2.2722  loss: 0.0269  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0269
2025/08/08 13:38:11 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:38:11 - mmengine - INFO - Epoch(train)  [78][28/28]  base_lr: 1.3779e-03 lr: 1.3779e-03  eta: 0:29:30  time: 1.8281  data_time: 1.1111  memory: 30731  grad_norm: 2.2894  loss: 0.0250  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0250
2025/08/08 13:38:11 - mmengine - INFO - Saving checkpoint at 78 epochs
2025/08/08 13:38:23 - mmengine - INFO - Epoch(val) [78][10/10]    acc/top1: 0.8836  acc/top5: 1.0000  acc/mean1: 0.8846  data_time: 0.5730  time: 0.6536
2025/08/08 13:39:25 - mmengine - INFO - Epoch(train)  [79][20/28]  base_lr: 1.2659e-03 lr: 1.2659e-03  eta: 0:28:33  time: 3.0808  data_time: 2.1058  memory: 30731  grad_norm: 2.0700  loss: 0.0136  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0136
2025/08/08 13:39:32 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:39:32 - mmengine - INFO - Epoch(train)  [79][28/28]  base_lr: 1.2659e-03 lr: 1.2659e-03  eta: 0:28:06  time: 2.0320  data_time: 1.1676  memory: 30731  grad_norm: 2.1752  loss: 0.0147  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0147
2025/08/08 13:39:32 - mmengine - INFO - Saving checkpoint at 79 epochs
2025/08/08 13:39:44 - mmengine - INFO - Epoch(val) [79][10/10]    acc/top1: 0.8884  acc/top5: 1.0000  acc/mean1: 0.8883  data_time: 0.5305  time: 0.6092
2025/08/08 13:40:47 - mmengine - INFO - Epoch(train)  [80][20/28]  base_lr: 1.1580e-03 lr: 1.1580e-03  eta: 0:27:10  time: 3.1405  data_time: 2.1989  memory: 30731  grad_norm: 2.2621  loss: 0.0155  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0155
2025/08/08 13:40:54 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:40:54 - mmengine - INFO - Epoch(train)  [80][28/28]  base_lr: 1.1580e-03 lr: 1.1580e-03  eta: 0:26:43  time: 2.0897  data_time: 1.2715  memory: 30731  grad_norm: 2.1835  loss: 0.0121  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0121
2025/08/08 13:40:54 - mmengine - INFO - Saving checkpoint at 80 epochs
2025/08/08 13:41:06 - mmengine - INFO - Epoch(val) [80][10/10]    acc/top1: 0.8856  acc/top5: 1.0000  acc/mean1: 0.8872  data_time: 0.6168  time: 0.6952
2025/08/08 13:42:09 - mmengine - INFO - Epoch(train)  [81][20/28]  base_lr: 1.0543e-03 lr: 1.0543e-03  eta: 0:25:47  time: 3.1529  data_time: 2.2658  memory: 30731  grad_norm: 2.4938  loss: 0.0190  top1_acc: 0.9688  top5_acc: 1.0000  loss_cls: 0.0190
2025/08/08 13:42:16 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:42:16 - mmengine - INFO - Epoch(train)  [81][28/28]  base_lr: 1.0543e-03 lr: 1.0543e-03  eta: 0:25:21  time: 2.0921  data_time: 1.3179  memory: 30731  grad_norm: 2.3022  loss: 0.0173  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0173
2025/08/08 13:42:17 - mmengine - INFO - Saving checkpoint at 81 epochs
2025/08/08 13:42:29 - mmengine - INFO - Epoch(val) [81][10/10]    acc/top1: 0.8856  acc/top5: 1.0000  acc/mean1: 0.8858  data_time: 0.6301  time: 0.7108
2025/08/08 13:43:32 - mmengine - INFO - Epoch(train)  [82][20/28]  base_lr: 9.5492e-04 lr: 9.5492e-04  eta: 0:24:25  time: 3.1535  data_time: 2.2977  memory: 30731  grad_norm: 2.0047  loss: 0.0184  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0184
2025/08/08 13:43:40 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:43:40 - mmengine - INFO - Epoch(train)  [82][28/28]  base_lr: 9.5492e-04 lr: 9.5492e-04  eta: 0:23:59  time: 2.1520  data_time: 1.3868  memory: 30731  grad_norm: 2.1617  loss: 0.0176  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0176
2025/08/08 13:43:40 - mmengine - INFO - Saving checkpoint at 82 epochs
2025/08/08 13:43:52 - mmengine - INFO - Epoch(val) [82][10/10]    acc/top1: 0.8791  acc/top5: 1.0000  acc/mean1: 0.8814  data_time: 0.6217  time: 0.7017
2025/08/08 13:44:56 - mmengine - INFO - Epoch(train)  [83][20/28]  base_lr: 8.5996e-04 lr: 8.5996e-04  eta: 0:23:03  time: 3.1925  data_time: 2.3869  memory: 30731  grad_norm: 2.1379  loss: 0.0135  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0135
2025/08/08 13:45:02 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:45:02 - mmengine - INFO - Epoch(train)  [83][28/28]  base_lr: 8.5996e-04 lr: 8.5996e-04  eta: 0:22:37  time: 2.1799  data_time: 1.4243  memory: 30731  grad_norm: 1.9752  loss: 0.0128  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0128
2025/08/08 13:45:03 - mmengine - INFO - Saving checkpoint at 83 epochs
2025/08/08 13:45:15 - mmengine - INFO - Epoch(val) [83][10/10]    acc/top1: 0.8901  acc/top5: 1.0000  acc/mean1: 0.8907  data_time: 0.6150  time: 0.6946
2025/08/08 13:46:18 - mmengine - INFO - Epoch(train)  [84][20/28]  base_lr: 7.6952e-04 lr: 7.6952e-04  eta: 0:21:41  time: 3.1728  data_time: 2.3111  memory: 30731  grad_norm: 2.1674  loss: 0.0206  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0206
2025/08/08 13:46:25 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:46:25 - mmengine - INFO - Epoch(train)  [84][28/28]  base_lr: 7.6952e-04 lr: 7.6952e-04  eta: 0:21:15  time: 2.1602  data_time: 1.3534  memory: 30731  grad_norm: 2.2749  loss: 0.0213  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0213
2025/08/08 13:46:26 - mmengine - INFO - Saving checkpoint at 84 epochs
2025/08/08 13:46:38 - mmengine - INFO - Epoch(val) [84][10/10]    acc/top1: 0.8897  acc/top5: 1.0000  acc/mean1: 0.8908  data_time: 0.6223  time: 0.7027
2025/08/08 13:47:41 - mmengine - INFO - Epoch(train)  [85][20/28]  base_lr: 6.8372e-04 lr: 6.8372e-04  eta: 0:20:19  time: 3.1372  data_time: 2.3022  memory: 30731  grad_norm: 1.7207  loss: 0.0101  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0101
2025/08/08 13:47:49 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:47:49 - mmengine - INFO - Epoch(train)  [85][28/28]  base_lr: 6.8372e-04 lr: 6.8372e-04  eta: 0:19:54  time: 2.1724  data_time: 1.4152  memory: 30731  grad_norm: 1.8526  loss: 0.0136  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0136
2025/08/08 13:47:49 - mmengine - INFO - Saving checkpoint at 85 epochs
2025/08/08 13:48:01 - mmengine - INFO - Epoch(val) [85][10/10]    acc/top1: 0.8892  acc/top5: 1.0000  acc/mean1: 0.8901  data_time: 0.6080  time: 0.6900
2025/08/08 13:49:03 - mmengine - INFO - Epoch(train)  [86][20/28]  base_lr: 6.0263e-04 lr: 6.0263e-04  eta: 0:18:58  time: 3.1279  data_time: 2.2838  memory: 30731  grad_norm: 1.7707  loss: 0.0144  top1_acc: 0.9844  top5_acc: 1.0000  loss_cls: 0.0144
2025/08/08 13:49:11 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:49:11 - mmengine - INFO - Epoch(train)  [86][28/28]  base_lr: 6.0263e-04 lr: 6.0263e-04  eta: 0:18:33  time: 2.1134  data_time: 1.3673  memory: 30731  grad_norm: 1.9067  loss: 0.0120  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0120
2025/08/08 13:49:11 - mmengine - INFO - Saving checkpoint at 86 epochs
2025/08/08 13:49:23 - mmengine - INFO - Epoch(val) [86][10/10]    acc/top1: 0.8905  acc/top5: 1.0000  acc/mean1: 0.8924  data_time: 0.6205  time: 0.6998
2025/08/08 13:50:25 - mmengine - INFO - Epoch(train)  [87][20/28]  base_lr: 5.2635e-04 lr: 5.2635e-04  eta: 0:17:37  time: 3.1084  data_time: 2.2944  memory: 30731  grad_norm: 1.8230  loss: 0.0128  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0128
2025/08/08 13:50:33 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:50:33 - mmengine - INFO - Epoch(train)  [87][28/28]  base_lr: 5.2635e-04 lr: 5.2635e-04  eta: 0:17:12  time: 2.1737  data_time: 1.3680  memory: 30731  grad_norm: 2.0428  loss: 0.0121  top1_acc: 0.9630  top5_acc: 1.0000  loss_cls: 0.0121
2025/08/08 13:50:33 - mmengine - INFO - Saving checkpoint at 87 epochs
2025/08/08 13:50:45 - mmengine - INFO - Epoch(val) [87][10/10]    acc/top1: 0.8909  acc/top5: 1.0000  acc/mean1: 0.8910  data_time: 0.6465  time: 0.7271
2025/08/08 13:51:48 - mmengine - INFO - Epoch(train)  [88][20/28]  base_lr: 4.5497e-04 lr: 4.5497e-04  eta: 0:16:16  time: 3.1406  data_time: 2.3659  memory: 30731  grad_norm: 1.8493  loss: 0.0142  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0142
2025/08/08 13:51:56 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:51:56 - mmengine - INFO - Epoch(train)  [88][28/28]  base_lr: 4.5497e-04 lr: 4.5497e-04  eta: 0:15:51  time: 2.1902  data_time: 1.4504  memory: 30731  grad_norm: 2.2201  loss: 0.0120  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0120
2025/08/08 13:51:56 - mmengine - INFO - Saving checkpoint at 88 epochs
2025/08/08 13:52:08 - mmengine - INFO - Epoch(val) [88][10/10]    acc/top1: 0.8880  acc/top5: 1.0000  acc/mean1: 0.8897  data_time: 0.7073  time: 0.7859
2025/08/08 13:53:12 - mmengine - INFO - Epoch(train)  [89][20/28]  base_lr: 3.8855e-04 lr: 3.8855e-04  eta: 0:14:55  time: 3.1798  data_time: 2.2259  memory: 30731  grad_norm: 1.9842  loss: 0.0172  top1_acc: 0.9844  top5_acc: 1.0000  loss_cls: 0.0172
2025/08/08 13:53:19 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:53:19 - mmengine - INFO - Epoch(train)  [89][28/28]  base_lr: 3.8855e-04 lr: 3.8855e-04  eta: 0:14:31  time: 2.1581  data_time: 1.2611  memory: 30731  grad_norm: 2.3220  loss: 0.0259  top1_acc: 0.8704  top5_acc: 1.0000  loss_cls: 0.0259
2025/08/08 13:53:19 - mmengine - INFO - Saving checkpoint at 89 epochs
2025/08/08 13:53:31 - mmengine - INFO - Epoch(val) [89][10/10]    acc/top1: 0.8795  acc/top5: 1.0000  acc/mean1: 0.8805  data_time: 0.6700  time: 0.7491
2025/08/08 13:54:35 - mmengine - INFO - Epoch(train)  [90][20/28]  base_lr: 3.2718e-04 lr: 3.2718e-04  eta: 0:13:35  time: 3.1929  data_time: 2.3114  memory: 30731  grad_norm: 1.6553  loss: 0.0071  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0071
2025/08/08 13:54:42 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:54:42 - mmengine - INFO - Epoch(train)  [90][28/28]  base_lr: 3.2718e-04 lr: 3.2718e-04  eta: 0:13:11  time: 2.1526  data_time: 1.3687  memory: 30731  grad_norm: 1.8440  loss: 0.0087  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0087
2025/08/08 13:54:42 - mmengine - INFO - Saving checkpoint at 90 epochs
2025/08/08 13:54:54 - mmengine - INFO - Epoch(val) [90][10/10]    acc/top1: 0.8884  acc/top5: 1.0000  acc/mean1: 0.8895  data_time: 0.6747  time: 0.7543
2025/08/08 13:55:58 - mmengine - INFO - Epoch(train)  [91][20/28]  base_lr: 2.7091e-04 lr: 2.7091e-04  eta: 0:12:15  time: 3.1991  data_time: 2.3684  memory: 30731  grad_norm: 1.7983  loss: 0.0167  top1_acc: 0.9844  top5_acc: 1.0000  loss_cls: 0.0167
2025/08/08 13:56:05 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:56:05 - mmengine - INFO - Epoch(train)  [91][28/28]  base_lr: 2.7091e-04 lr: 2.7091e-04  eta: 0:11:51  time: 2.1784  data_time: 1.4248  memory: 30731  grad_norm: 1.9313  loss: 0.0170  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0170
2025/08/08 13:56:05 - mmengine - INFO - Saving checkpoint at 91 epochs
2025/08/08 13:56:17 - mmengine - INFO - Epoch(val) [91][10/10]    acc/top1: 0.8888  acc/top5: 1.0000  acc/mean1: 0.8895  data_time: 0.6736  time: 0.7536
2025/08/08 13:57:20 - mmengine - INFO - Epoch(train)  [92][20/28]  base_lr: 2.1982e-04 lr: 2.1982e-04  eta: 0:10:55  time: 3.1250  data_time: 2.3390  memory: 30731  grad_norm: 1.9365  loss: 0.0113  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0113
2025/08/08 13:57:28 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:57:28 - mmengine - INFO - Epoch(train)  [92][28/28]  base_lr: 2.1982e-04 lr: 2.1982e-04  eta: 0:10:31  time: 2.1664  data_time: 1.4340  memory: 30731  grad_norm: 1.7980  loss: 0.0077  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0077
2025/08/08 13:57:28 - mmengine - INFO - Saving checkpoint at 92 epochs
2025/08/08 13:57:40 - mmengine - INFO - Epoch(val) [92][10/10]    acc/top1: 0.8897  acc/top5: 1.0000  acc/mean1: 0.8908  data_time: 0.5966  time: 0.6759
2025/08/08 13:58:43 - mmengine - INFO - Epoch(train)  [93][20/28]  base_lr: 1.7396e-04 lr: 1.7396e-04  eta: 0:09:35  time: 3.1313  data_time: 2.3217  memory: 30731  grad_norm: 2.0087  loss: 0.0139  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0139
2025/08/08 13:58:51 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 13:58:51 - mmengine - INFO - Epoch(train)  [93][28/28]  base_lr: 1.7396e-04 lr: 1.7396e-04  eta: 0:09:11  time: 2.1694  data_time: 1.4217  memory: 30731  grad_norm: 1.7685  loss: 0.0149  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0149
2025/08/08 13:58:51 - mmengine - INFO - Saving checkpoint at 93 epochs
2025/08/08 13:59:03 - mmengine - INFO - Epoch(val) [93][10/10]    acc/top1: 0.8905  acc/top5: 1.0000  acc/mean1: 0.8917  data_time: 0.6882  time: 0.7689
2025/08/08 14:00:06 - mmengine - INFO - Epoch(train)  [94][20/28]  base_lr: 1.3337e-04 lr: 1.3337e-04  eta: 0:08:16  time: 3.1547  data_time: 2.3206  memory: 30731  grad_norm: 1.7562  loss: 0.0091  top1_acc: 0.9766  top5_acc: 1.0000  loss_cls: 0.0091
2025/08/08 14:00:14 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:00:14 - mmengine - INFO - Epoch(train)  [94][28/28]  base_lr: 1.3337e-04 lr: 1.3337e-04  eta: 0:07:52  time: 2.1911  data_time: 1.4225  memory: 30731  grad_norm: 1.9175  loss: 0.0110  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0110
2025/08/08 14:00:14 - mmengine - INFO - Saving checkpoint at 94 epochs
2025/08/08 14:00:26 - mmengine - INFO - Epoch(val) [94][10/10]    acc/top1: 0.8880  acc/top5: 1.0000  acc/mean1: 0.8896  data_time: 0.6718  time: 0.7509
2025/08/08 14:01:29 - mmengine - INFO - Epoch(train)  [95][20/28]  base_lr: 9.8100e-05 lr: 9.8100e-05  eta: 0:06:56  time: 3.1383  data_time: 2.3876  memory: 30731  grad_norm: 1.9673  loss: 0.0181  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0181
2025/08/08 14:01:37 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:01:37 - mmengine - INFO - Epoch(train)  [95][28/28]  base_lr: 9.8100e-05 lr: 9.8100e-05  eta: 0:06:33  time: 2.2088  data_time: 1.4924  memory: 30731  grad_norm: 2.1206  loss: 0.0169  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0169
2025/08/08 14:01:37 - mmengine - INFO - Saving checkpoint at 95 epochs
2025/08/08 14:01:50 - mmengine - INFO - Epoch(val) [95][10/10]    acc/top1: 0.8872  acc/top5: 1.0000  acc/mean1: 0.8876  data_time: 0.6735  time: 0.7519
2025/08/08 14:02:53 - mmengine - INFO - Epoch(train)  [96][20/28]  base_lr: 6.8193e-05 lr: 6.8193e-05  eta: 0:05:37  time: 3.1790  data_time: 2.2734  memory: 30731  grad_norm: 1.6721  loss: 0.0167  top1_acc: 0.9922  top5_acc: 1.0000  loss_cls: 0.0167
2025/08/08 14:03:01 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:03:01 - mmengine - INFO - Epoch(train)  [96][28/28]  base_lr: 6.8193e-05 lr: 6.8193e-05  eta: 0:05:14  time: 2.1460  data_time: 1.3199  memory: 30731  grad_norm: 1.7160  loss: 0.0167  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0167
2025/08/08 14:03:01 - mmengine - INFO - Saving checkpoint at 96 epochs
2025/08/08 14:03:13 - mmengine - INFO - Epoch(val) [96][10/10]    acc/top1: 0.8884  acc/top5: 1.0000  acc/mean1: 0.8891  data_time: 0.6719  time: 0.7521
2025/08/08 14:04:16 - mmengine - INFO - Epoch(train)  [97][20/28]  base_lr: 4.3680e-05 lr: 4.3680e-05  eta: 0:04:18  time: 3.1716  data_time: 2.3225  memory: 30731  grad_norm: 1.6876  loss: 0.0082  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0082
2025/08/08 14:04:24 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:04:24 - mmengine - INFO - Epoch(train)  [97][28/28]  base_lr: 4.3680e-05 lr: 4.3680e-05  eta: 0:03:55  time: 2.1575  data_time: 1.3865  memory: 30731  grad_norm: 1.8146  loss: 0.0124  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0124
2025/08/08 14:04:24 - mmengine - INFO - Saving checkpoint at 97 epochs
2025/08/08 14:04:36 - mmengine - INFO - Epoch(val) [97][10/10]    acc/top1: 0.8921  acc/top5: 1.0000  acc/mean1: 0.8930  data_time: 0.6533  time: 0.7330
2025/08/08 14:05:39 - mmengine - INFO - Epoch(train)  [98][20/28]  base_lr: 2.4585e-05 lr: 2.4585e-05  eta: 0:02:59  time: 3.1414  data_time: 2.2348  memory: 30731  grad_norm: 1.6621  loss: 0.0091  top1_acc: 0.9766  top5_acc: 1.0000  loss_cls: 0.0091
2025/08/08 14:05:46 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:05:46 - mmengine - INFO - Epoch(train)  [98][28/28]  base_lr: 2.4585e-05 lr: 2.4585e-05  eta: 0:02:36  time: 2.1157  data_time: 1.3067  memory: 30731  grad_norm: 1.7527  loss: 0.0089  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0089
2025/08/08 14:05:46 - mmengine - INFO - Saving checkpoint at 98 epochs
2025/08/08 14:05:58 - mmengine - INFO - Epoch(val) [98][10/10]    acc/top1: 0.8901  acc/top5: 1.0000  acc/mean1: 0.8907  data_time: 0.6582  time: 0.7379
2025/08/08 14:07:01 - mmengine - INFO - Epoch(train)  [99][20/28]  base_lr: 1.0932e-05 lr: 1.0932e-05  eta: 0:01:40  time: 3.1451  data_time: 2.2731  memory: 30731  grad_norm: 1.9871  loss: 0.0146  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0146
2025/08/08 14:07:09 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:07:09 - mmengine - INFO - Epoch(train)  [99][28/28]  base_lr: 1.0932e-05 lr: 1.0932e-05  eta: 0:01:18  time: 2.1592  data_time: 1.3681  memory: 30731  grad_norm: 1.9013  loss: 0.0138  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0138
2025/08/08 14:07:09 - mmengine - INFO - Saving checkpoint at 99 epochs
2025/08/08 14:07:21 - mmengine - INFO - Epoch(val) [99][10/10]    acc/top1: 0.8880  acc/top5: 1.0000  acc/mean1: 0.8888  data_time: 0.6257  time: 0.7045
2025/08/08 14:08:25 - mmengine - INFO - Epoch(train) [100][20/28]  base_lr: 2.7337e-06 lr: 2.7337e-06  eta: 0:00:22  time: 3.1719  data_time: 2.2579  memory: 30731  grad_norm: 1.5539  loss: 0.0061  top1_acc: 1.0000  top5_acc: 1.0000  loss_cls: 0.0061
2025/08/08 14:08:32 - mmengine - INFO - Exp name: multimodal_poseGCN-rgbR50_fusion_focal_20250808_112959
2025/08/08 14:08:32 - mmengine - INFO - Epoch(train) [100][28/28]  base_lr: 2.7337e-06 lr: 2.7337e-06  eta: 0:00:00  time: 2.1448  data_time: 1.3464  memory: 30731  grad_norm: 2.0304  loss: 0.0061  top1_acc: 0.9815  top5_acc: 1.0000  loss_cls: 0.0061
2025/08/08 14:08:33 - mmengine - INFO - Saving checkpoint at 100 epochs
2025/08/08 14:08:45 - mmengine - INFO - Epoch(val) [100][10/10]    acc/top1: 0.8880  acc/top5: 1.0000  acc/mean1: 0.8889  data_time: 0.7226  time: 0.8022
