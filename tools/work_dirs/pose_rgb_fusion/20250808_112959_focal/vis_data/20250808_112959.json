{"base_lr": 0.001, "lr": 0.001, "data_time": 16.03292796611786, "grad_norm": 7.77725510597229, "loss": 0.5499759316444397, "top1_acc": 0.359375, "top5_acc": 1.0, "loss_cls": 0.5499759316444397, "time": 28.23916140794754, "epoch": 1, "iter": 20, "memory": 30737, "step": 20}
{"base_lr": 0.001, "lr": 0.001, "data_time": 9.759831273555756, "grad_norm": 7.326476740837097, "loss": 0.5464336782693863, "top1_acc": 0.4074074074074074, "top5_acc": 1.0, "loss_cls": 0.5464336782693863, "time": 18.635299837589265, "epoch": 1, "iter": 28, "memory": 30731, "step": 28}
{"acc/top1": 0.3330628803245436, "acc/top5": 1.0, "acc/mean1": 0.3333333333333333, "data_time": 12.021412920951843, "time": 12.103832936286926, "step": 1}
{"base_lr": 0.0032500000000000003, "lr": 0.0032500000000000003, "data_time": 4.4145384430885315, "grad_norm": 5.516591238975525, "loss": 0.5391655921936035, "top1_acc": 0.328125, "top5_acc": 1.0, "loss_cls": 0.5391655921936035, "time": 5.126478970050812, "epoch": 2, "iter": 48, "memory": 30731, "step": 48}
{"base_lr": 0.0032500000000000003, "lr": 0.0032500000000000003, "data_time": 3.2392000794410705, "grad_norm": 5.394490790367127, "loss": 0.5364122718572617, "top1_acc": 0.35185185185185186, "top5_acc": 1.0, "loss_cls": 0.5364122718572617, "time": 3.930080699920654, "epoch": 2, "iter": 56, "memory": 30731, "step": 56}
{"acc/top1": 0.38336713995943206, "acc/top5": 1.0, "acc/mean1": 0.3373934226552984, "data_time": 2.3300816362554375, "time": 2.4094605445861816, "step": 2}
{"base_lr": 0.0055000000000000005, "lr": 0.0055000000000000005, "data_time": 3.400193953514099, "grad_norm": 5.627525997161865, "loss": 0.531806230545044, "top1_acc": 0.3984375, "top5_acc": 1.0, "loss_cls": 0.531806230545044, "time": 4.133628976345062, "epoch": 3, "iter": 76, "memory": 30731, "step": 76}
{"base_lr": 0.0055000000000000005, "lr": 0.0055000000000000005, "data_time": 2.266901898384094, "grad_norm": 6.329617333412171, "loss": 0.5290458738803864, "top1_acc": 0.3888888888888889, "top5_acc": 1.0, "loss_cls": 0.5290458738803864, "time": 2.9412062764167786, "epoch": 3, "iter": 84, "memory": 30731, "step": 84}
{"acc/top1": 0.42758620689655175, "acc/top5": 1.0, "acc/mean1": 0.3867195382575737, "data_time": 1.2518302527340976, "time": 1.3349147059700706, "step": 3}
{"base_lr": 0.007750000000000001, "lr": 0.007750000000000001, "data_time": 2.8792226552963256, "grad_norm": 7.45352075099945, "loss": 0.4858317390084267, "top1_acc": 0.5625, "top5_acc": 1.0, "loss_cls": 0.4858317390084267, "time": 3.681491458415985, "epoch": 4, "iter": 104, "memory": 30731, "step": 104}
{"base_lr": 0.007750000000000001, "lr": 0.007750000000000001, "data_time": 1.7707479357719422, "grad_norm": 8.045764946937561, "loss": 0.44551871418952943, "top1_acc": 0.5, "top5_acc": 1.0, "loss_cls": 0.44551871418952943, "time": 2.511534905433655, "epoch": 4, "iter": 112, "memory": 30731, "step": 112}
{"acc/top1": 0.6340770791075051, "acc/top5": 1.0, "acc/mean1": 0.627436899323905, "data_time": 1.1681026328693738, "time": 1.249001914804632, "step": 4}
{"base_lr": 0.01, "lr": 0.01, "data_time": 3.514984464645386, "grad_norm": 6.495286965370179, "loss": 0.33761489391326904, "top1_acc": 0.609375, "top5_acc": 1.0, "loss_cls": 0.33761489391326904, "time": 4.298027157783508, "epoch": 5, "iter": 132, "memory": 30731, "step": 132}
{"base_lr": 0.01, "lr": 0.01, "data_time": 2.367343235015869, "grad_norm": 6.236961150169373, "loss": 0.32069238275289536, "top1_acc": 0.7037037037037037, "top5_acc": 1.0, "loss_cls": 0.32069238275289536, "time": 3.095550239086151, "epoch": 5, "iter": 140, "memory": 30731, "step": 140}
{"acc/top1": 0.701419878296146, "acc/top5": 1.0, "acc/mean1": 0.7072154879232088, "data_time": 1.1658266891132703, "time": 1.2464088743383235, "step": 5}
{"base_lr": 0.01, "lr": 0.01, "data_time": 3.1831097483634947, "grad_norm": 5.521604514122009, "loss": 0.28313023746013644, "top1_acc": 0.703125, "top5_acc": 1.0, "loss_cls": 0.28313023746013644, "time": 3.9831050753593447, "epoch": 6, "iter": 160, "memory": 30731, "step": 160}
{"base_lr": 0.01, "lr": 0.01, "data_time": 2.087196171283722, "grad_norm": 5.493326425552368, "loss": 0.27120986953377724, "top1_acc": 0.7407407407407407, "top5_acc": 1.0, "loss_cls": 0.27120986953377724, "time": 2.7961637139320374, "epoch": 6, "iter": 168, "memory": 30731, "step": 168}
{"acc/top1": 0.77079107505071, "acc/top5": 1.0, "acc/mean1": 0.7703075708978586, "data_time": 1.0368169004266912, "time": 1.1171598434448242, "step": 6}
{"base_lr": 0.00999726628670463, "lr": 0.00999726628670463, "data_time": 3.2516593456268312, "grad_norm": 5.271474075317383, "loss": 0.24062329456210135, "top1_acc": 0.8515625, "top5_acc": 1.0, "loss_cls": 0.24062329456210135, "time": 3.969840037822723, "epoch": 7, "iter": 188, "memory": 30731, "step": 188}
{"base_lr": 0.00999726628670463, "lr": 0.00999726628670463, "data_time": 2.2093878984451294, "grad_norm": 5.382815170288086, "loss": 0.23601580560207366, "top1_acc": 0.7777777777777778, "top5_acc": 1.0, "loss_cls": 0.23601580560207366, "time": 2.9143427133560182, "epoch": 7, "iter": 196, "memory": 30731, "step": 196}
{"acc/top1": 0.7910750507099391, "acc/top5": 1.0, "acc/mean1": 0.7993195912188288, "data_time": 1.314681963487105, "time": 1.396725827997381, "step": 7}
{"base_lr": 0.009989068136093873, "lr": 0.009989068136093873, "data_time": 3.2309574246406556, "grad_norm": 5.178297328948974, "loss": 0.21750686839222907, "top1_acc": 0.7734375, "top5_acc": 1.0, "loss_cls": 0.21750686839222907, "time": 4.050678813457489, "epoch": 8, "iter": 216, "memory": 30731, "step": 216}
{"base_lr": 0.009989068136093873, "lr": 0.009989068136093873, "data_time": 1.8827860593795775, "grad_norm": 5.165009832382202, "loss": 0.22094553112983703, "top1_acc": 0.7592592592592593, "top5_acc": 1.0, "loss_cls": 0.22094553112983703, "time": 2.637550377845764, "epoch": 8, "iter": 224, "memory": 30731, "step": 224}
{"acc/top1": 0.8109533468559837, "acc/top5": 1.0, "acc/mean1": 0.8122711845931249, "data_time": 1.0667682777751575, "time": 1.1470549540086226, "step": 8}
{"base_lr": 0.009975414512725057, "lr": 0.009975414512725057, "data_time": 3.1514323234558104, "grad_norm": 4.60155155658722, "loss": 0.20480313822627066, "top1_acc": 0.8046875, "top5_acc": 1.0, "loss_cls": 0.20480313822627066, "time": 3.9333483457565306, "epoch": 9, "iter": 244, "memory": 30731, "step": 244}
{"base_lr": 0.009975414512725057, "lr": 0.009975414512725057, "data_time": 2.0197471022605895, "grad_norm": 4.729681038856507, "loss": 0.2028687469661236, "top1_acc": 0.7592592592592593, "top5_acc": 1.0, "loss_cls": 0.2028687469661236, "time": 2.735746908187866, "epoch": 9, "iter": 252, "memory": 30731, "step": 252}
{"acc/top1": 0.8231237322515212, "acc/top5": 1.0, "acc/mean1": 0.8249237119037199, "data_time": 1.1254045096310703, "time": 1.2074349576776677, "step": 9}
{"base_lr": 0.009956320346634876, "lr": 0.009956320346634876, "data_time": 3.2187137484550474, "grad_norm": 4.439796090126038, "loss": 0.19134241938591004, "top1_acc": 0.8359375, "top5_acc": 1.0, "loss_cls": 0.19134241938591004, "time": 3.994500195980072, "epoch": 10, "iter": 272, "memory": 30731, "step": 272}
{"base_lr": 0.009956320346634876, "lr": 0.009956320346634876, "data_time": 1.7925671219825745, "grad_norm": 4.384396433830261, "loss": 0.19193477556109428, "top1_acc": 0.8333333333333334, "top5_acc": 1.0, "loss_cls": 0.19193477556109428, "time": 2.497864282131195, "epoch": 10, "iter": 280, "memory": 30731, "step": 280}
{"acc/top1": 0.8425963488843813, "acc/top5": 1.0, "acc/mean1": 0.8477425549061754, "data_time": 0.850004412911155, "time": 0.9318595366044478, "step": 10}
{"base_lr": 0.009931806517013612, "lr": 0.009931806517013612, "data_time": 2.4554925918579102, "grad_norm": 4.204274749755859, "loss": 0.1819884330034256, "top1_acc": 0.8125, "top5_acc": 1.0, "loss_cls": 0.1819884330034256, "time": 3.2947225093841555, "epoch": 11, "iter": 300, "memory": 30731, "step": 300}
{"base_lr": 0.009931806517013612, "lr": 0.009931806517013612, "data_time": 1.3683926105499267, "grad_norm": 4.297502958774567, "loss": 0.18222390711307526, "top1_acc": 0.8518518518518519, "top5_acc": 1.0, "loss_cls": 0.18222390711307526, "time": 2.1077661752700805, "epoch": 11, "iter": 308, "memory": 30731, "step": 308}
{"acc/top1": 0.8515212981744422, "acc/top5": 1.0, "acc/mean1": 0.8558827083211012, "data_time": 0.8904135010459207, "time": 0.9718753424557772, "step": 11}
{"base_lr": 0.009901899829374047, "lr": 0.009901899829374047, "data_time": 2.4928828120231628, "grad_norm": 4.240518593788147, "loss": 0.17046746388077735, "top1_acc": 0.8671875, "top5_acc": 1.0, "loss_cls": 0.17046746388077735, "time": 3.2602924346923827, "epoch": 12, "iter": 328, "memory": 30731, "step": 328}
{"base_lr": 0.009901899829374047, "lr": 0.009901899829374047, "data_time": 1.3814691185951233, "grad_norm": 4.352979242801666, "loss": 0.1608167566359043, "top1_acc": 0.8518518518518519, "top5_acc": 1.0, "loss_cls": 0.1608167566359043, "time": 2.0954700112342834, "epoch": 12, "iter": 336, "memory": 30731, "step": 336}
{"acc/top1": 0.8361054766734279, "acc/top5": 1.0, "acc/mean1": 0.8420698902072633, "data_time": 0.8422805395993319, "time": 0.9228679917075417, "step": 12}
{"base_lr": 0.009866632986240029, "lr": 0.009866632986240029, "data_time": 2.769343984127045, "grad_norm": 4.348158824443817, "loss": 0.1835515819489956, "top1_acc": 0.8359375, "top5_acc": 1.0, "loss_cls": 0.1835515819489956, "time": 3.5867188215255736, "epoch": 13, "iter": 356, "memory": 30731, "step": 356}
{"base_lr": 0.009866632986240029, "lr": 0.009866632986240029, "data_time": 1.6566051125526429, "grad_norm": 4.738436317443847, "loss": 0.18005514964461328, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.18005514964461328, "time": 2.396607482433319, "epoch": 13, "iter": 364, "memory": 30731, "step": 364}
{"acc/top1": 0.8381338742393509, "acc/top5": 1.0, "acc/mean1": 0.8423281382606685, "data_time": 0.7052627910267223, "time": 0.7867579026655718, "step": 13}
{"base_lr": 0.009826044551386743, "lr": 0.009826044551386743, "data_time": 2.5391828417778015, "grad_norm": 4.145473396778106, "loss": 0.1904038019478321, "top1_acc": 0.796875, "top5_acc": 1.0, "loss_cls": 0.1904038019478321, "time": 3.370437300205231, "epoch": 14, "iter": 384, "memory": 30731, "step": 384}
{"base_lr": 0.009826044551386743, "lr": 0.009826044551386743, "data_time": 1.5599948048591614, "grad_norm": 4.05658940076828, "loss": 0.18333845734596252, "top1_acc": 0.8703703703703703, "top5_acc": 1.0, "loss_cls": 0.18333845734596252, "time": 2.3684680581092836, "epoch": 14, "iter": 392, "memory": 30731, "step": 392}
{"acc/top1": 0.8385395537525355, "acc/top5": 1.0, "acc/mean1": 0.840133806099795, "data_time": 0.6907377676530317, "time": 0.774455807425759, "step": 14}
{"base_lr": 0.009780178907671787, "lr": 0.009780178907671787, "data_time": 2.7897042632102966, "grad_norm": 4.041836833953857, "loss": 0.16313496306538583, "top1_acc": 0.8359375, "top5_acc": 1.0, "loss_cls": 0.16313496306538583, "time": 3.60173659324646, "epoch": 15, "iter": 412, "memory": 30731, "step": 412}
{"base_lr": 0.009780178907671787, "lr": 0.009780178907671787, "data_time": 1.907488179206848, "grad_norm": 3.980292570590973, "loss": 0.1666568137705326, "top1_acc": 0.8518518518518519, "top5_acc": 1.0, "loss_cls": 0.1666568137705326, "time": 2.6532222628593445, "epoch": 15, "iter": 420, "memory": 30731, "step": 420}
{"acc/top1": 0.8369168356997971, "acc/top5": 1.0, "acc/mean1": 0.8345607555728143, "data_time": 0.8033271486108954, "time": 0.884421933781017, "step": 15}
{"base_lr": 0.009729086208503173, "lr": 0.009729086208503173, "data_time": 2.63992555141449, "grad_norm": 3.9013758301734924, "loss": 0.16172654069960118, "top1_acc": 0.8359375, "top5_acc": 1.0, "loss_cls": 0.16172654069960118, "time": 3.47853342294693, "epoch": 16, "iter": 440, "memory": 30731, "step": 440}
{"base_lr": 0.009729086208503173, "lr": 0.009729086208503173, "data_time": 1.758635938167572, "grad_norm": 4.096831023693085, "loss": 0.1606579065322876, "top1_acc": 0.8333333333333334, "top5_acc": 1.0, "loss_cls": 0.1606579065322876, "time": 2.52640243768692, "epoch": 16, "iter": 448, "memory": 30731, "step": 448}
{"acc/top1": 0.8572008113590264, "acc/top5": 1.0, "acc/mean1": 0.8596203276447344, "data_time": 0.910460580479015, "time": 0.9907585057345304, "step": 16}
{"base_lr": 0.009672822322997305, "lr": 0.009672822322997305, "data_time": 2.976749527454376, "grad_norm": 3.813968336582184, "loss": 0.1583085834980011, "top1_acc": 0.890625, "top5_acc": 1.0, "loss_cls": 0.1583085834980011, "time": 3.717971754074097, "epoch": 17, "iter": 468, "memory": 30731, "step": 468}
{"base_lr": 0.009672822322997305, "lr": 0.009672822322997305, "data_time": 1.826128113269806, "grad_norm": 3.805166745185852, "loss": 0.16110683232545853, "top1_acc": 0.8703703703703703, "top5_acc": 1.0, "loss_cls": 0.16110683232545853, "time": 2.518568050861359, "epoch": 17, "iter": 476, "memory": 30731, "step": 476}
{"acc/top1": 0.845841784989858, "acc/top5": 1.0, "acc/mean1": 0.845550564179947, "data_time": 1.0191800594329834, "time": 1.0987679091366855, "step": 17}
{"base_lr": 0.009611448774886925, "lr": 0.009611448774886925, "data_time": 2.8458679556846618, "grad_norm": 3.7968276500701905, "loss": 0.14979271814227105, "top1_acc": 0.84375, "top5_acc": 1.0, "loss_cls": 0.14979271814227105, "time": 3.661152017116547, "epoch": 18, "iter": 496, "memory": 30731, "step": 496}
{"base_lr": 0.009611448774886925, "lr": 0.009611448774886925, "data_time": 1.7640490174293517, "grad_norm": 3.9306480050086976, "loss": 0.15170367956161498, "top1_acc": 0.8703703703703703, "top5_acc": 1.0, "loss_cls": 0.15170367956161498, "time": 2.496520483493805, "epoch": 18, "iter": 504, "memory": 30731, "step": 504}
{"acc/top1": 0.8296146044624746, "acc/top5": 1.0, "acc/mean1": 0.8293691096217656, "data_time": 0.9621706225655295, "time": 1.0534335049715908, "step": 18}
{"base_lr": 0.009545032675245814, "lr": 0.009545032675245814, "data_time": 2.802671253681183, "grad_norm": 3.5969470024108885, "loss": 0.142082504555583, "top1_acc": 0.875, "top5_acc": 1.0, "loss_cls": 0.142082504555583, "time": 3.679473304748535, "epoch": 19, "iter": 524, "memory": 30731, "step": 524}
{"base_lr": 0.009545032675245814, "lr": 0.009545032675245814, "data_time": 1.7899667501449585, "grad_norm": 3.9150766730308533, "loss": 0.15030606165528299, "top1_acc": 0.7962962962962963, "top5_acc": 1.0, "loss_cls": 0.15030606165528299, "time": 2.5576246857643126, "epoch": 19, "iter": 532, "memory": 30731, "step": 532}
{"acc/top1": 0.8523326572008113, "acc/top5": 1.0, "acc/mean1": 0.856164830678431, "data_time": 0.8352638374675404, "time": 0.9158370494842529, "step": 19}
{"base_lr": 0.009473646649103818, "lr": 0.009473646649103818, "data_time": 2.635661613941193, "grad_norm": 3.519813823699951, "loss": 0.1532331395894289, "top1_acc": 0.8828125, "top5_acc": 1.0, "loss_cls": 0.1532331395894289, "time": 3.4467958569526673, "epoch": 20, "iter": 552, "memory": 30731, "step": 552}
{"base_lr": 0.009473646649103818, "lr": 0.009473646649103818, "data_time": 1.6389065265655518, "grad_norm": 3.7386522769927977, "loss": 0.15382081530988218, "top1_acc": 0.7777777777777778, "top5_acc": 1.0, "loss_cls": 0.15382081530988218, "time": 2.415677857398987, "epoch": 20, "iter": 560, "memory": 30731, "step": 560}
{"acc/top1": 0.8661257606490872, "acc/top5": 1.0, "acc/mean1": 0.8657308706328298, "data_time": 0.8099537979472767, "time": 0.8900006250901655, "step": 20}
{"base_lr": 0.009397368756032446, "lr": 0.009397368756032446, "data_time": 2.4769155621528625, "grad_norm": 3.5293094515800476, "loss": 0.14563885405659677, "top1_acc": 0.8671875, "top5_acc": 1.0, "loss_cls": 0.14563885405659677, "time": 3.229240870475769, "epoch": 21, "iter": 580, "memory": 30731, "step": 580}
{"base_lr": 0.009397368756032446, "lr": 0.009397368756032446, "data_time": 1.3635352015495301, "grad_norm": 3.6852843880653383, "loss": 0.14326757453382016, "top1_acc": 0.8703703703703703, "top5_acc": 1.0, "loss_cls": 0.14326757453382016, "time": 2.0559461236000063, "epoch": 21, "iter": 588, "memory": 30731, "step": 588}
{"acc/top1": 0.840973630831643, "acc/top5": 1.0, "acc/mean1": 0.8427118671223651, "data_time": 0.745287071574818, "time": 0.8264411362734708, "step": 21}
{"base_lr": 0.00931628240478787, "lr": 0.00931628240478787, "data_time": 2.836931324005127, "grad_norm": 3.6555016160011293, "loss": 0.1339438997209072, "top1_acc": 0.84375, "top5_acc": 1.0, "loss_cls": 0.1339438997209072, "time": 3.713109242916107, "epoch": 22, "iter": 608, "memory": 30731, "step": 608}
{"base_lr": 0.00931628240478787, "lr": 0.00931628240478787, "data_time": 1.795991563796997, "grad_norm": 3.8149811029434204, "loss": 0.13938808105885983, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.13938808105885983, "time": 2.6099722504615785, "epoch": 22, "iter": 616, "memory": 30731, "step": 616}
{"acc/top1": 0.8466531440162272, "acc/top5": 1.0, "acc/mean1": 0.8521805982043364, "data_time": 0.8185771595348011, "time": 0.9000500548969615, "step": 22}
{"base_lr": 0.009230476262104678, "lr": 0.009230476262104678, "data_time": 2.982301342487335, "grad_norm": 3.535270035266876, "loss": 0.14461233504116536, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.14461233504116536, "time": 3.802938389778137, "epoch": 23, "iter": 636, "memory": 30731, "step": 636}
{"base_lr": 0.009230476262104678, "lr": 0.009230476262104678, "data_time": 1.9736464977264405, "grad_norm": 3.6276085138320924, "loss": 0.1367412954568863, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.1367412954568863, "time": 2.747880530357361, "epoch": 23, "iter": 644, "memory": 30731, "step": 644}
{"acc/top1": 0.8413793103448276, "acc/top5": 1.0, "acc/mean1": 0.8431966469871933, "data_time": 0.7631134553389116, "time": 0.8440906568007036, "step": 23}
{"base_lr": 0.009140044155740101, "lr": 0.009140044155740101, "data_time": 2.734418308734894, "grad_norm": 3.4135146737098694, "loss": 0.1331493016332388, "top1_acc": 0.875, "top5_acc": 1.0, "loss_cls": 0.1331493016332388, "time": 3.5377973318099976, "epoch": 24, "iter": 664, "memory": 30731, "step": 664}
{"base_lr": 0.009140044155740101, "lr": 0.009140044155740101, "data_time": 1.7686593294143678, "grad_norm": 3.6344789266586304, "loss": 0.12467630319297314, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.12467630319297314, "time": 2.4693865060806273, "epoch": 24, "iter": 672, "memory": 30731, "step": 672}
{"acc/top1": 0.8413793103448276, "acc/top5": 1.0, "acc/mean1": 0.8402799555962813, "data_time": 0.7972101298245516, "time": 0.8785929463126443, "step": 24}
{"base_lr": 0.009045084971874739, "lr": 0.009045084971874739, "data_time": 2.561112070083618, "grad_norm": 3.3328890442848205, "loss": 0.1370773632079363, "top1_acc": 0.9140625, "top5_acc": 1.0, "loss_cls": 0.1370773632079363, "time": 3.3974655628204347, "epoch": 25, "iter": 692, "memory": 30731, "step": 692}
{"base_lr": 0.009045084971874739, "lr": 0.009045084971874739, "data_time": 1.5938294410705567, "grad_norm": 3.3135340809822083, "loss": 0.12643125765025615, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.12643125765025615, "time": 2.340180814266205, "epoch": 25, "iter": 700, "memory": 30731, "step": 700}
{"acc/top1": 0.8515212981744422, "acc/top5": 1.0, "acc/mean1": 0.85249629072012, "data_time": 0.8576881885528564, "time": 0.9390588023445823, "step": 25}
{"base_lr": 0.00894570254698197, "lr": 0.00894570254698197, "data_time": 2.639795446395874, "grad_norm": 3.5244942784309385, "loss": 0.12809029333293437, "top1_acc": 0.890625, "top5_acc": 1.0, "loss_cls": 0.12809029333293437, "time": 3.436988115310669, "epoch": 26, "iter": 720, "memory": 30731, "step": 720}
{"base_lr": 0.00894570254698197, "lr": 0.00894570254698197, "data_time": 1.6935425519943237, "grad_norm": 3.5191908836364747, "loss": 0.12796115092933177, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.12796115092933177, "time": 2.4563059329986574, "epoch": 26, "iter": 728, "memory": 30731, "step": 728}
{"acc/top1": 0.8563894523326572, "acc/top5": 1.0, "acc/mean1": 0.8595215819411068, "data_time": 0.8209283785386519, "time": 0.9015952890569513, "step": 26}
{"base_lr": 0.008842005554284297, "lr": 0.008842005554284297, "data_time": 2.595365214347839, "grad_norm": 3.5128771543502806, "loss": 0.11615563817322254, "top1_acc": 0.859375, "top5_acc": 1.0, "loss_cls": 0.11615563817322254, "time": 3.446903681755066, "epoch": 27, "iter": 748, "memory": 30731, "step": 748}
{"base_lr": 0.008842005554284297, "lr": 0.008842005554284297, "data_time": 1.6455109357833861, "grad_norm": 3.5510982990264894, "loss": 0.11751436442136765, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.11751436442136765, "time": 2.445159637928009, "epoch": 27, "iter": 756, "memory": 30731, "step": 756}
{"acc/top1": 0.8584178498985802, "acc/top5": 1.0, "acc/mean1": 0.8629097003435554, "data_time": 0.7162634459408846, "time": 0.7971814979206432, "step": 27}
{"base_lr": 0.008734107384920772, "lr": 0.008734107384920772, "data_time": 2.7501796960830687, "grad_norm": 3.475364637374878, "loss": 0.12392547838389874, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.12392547838389874, "time": 3.4585251092910765, "epoch": 28, "iter": 776, "memory": 30731, "step": 776}
{"base_lr": 0.008734107384920772, "lr": 0.008734107384920772, "data_time": 1.82389634847641, "grad_norm": 3.7042572021484377, "loss": 0.1265641052275896, "top1_acc": 0.7962962962962963, "top5_acc": 1.0, "loss_cls": 0.1265641052275896, "time": 2.5194836854934692, "epoch": 28, "iter": 784, "memory": 30731, "step": 784}
{"acc/top1": 0.8738336713995943, "acc/top5": 1.0, "acc/mean1": 0.8726495638003106, "data_time": 0.8692602677778765, "time": 0.9492592161351984, "step": 28}
{"base_lr": 0.008622126023955448, "lr": 0.008622126023955448, "data_time": 2.470058524608612, "grad_norm": 3.4105844616889955, "loss": 0.12358618192374707, "top1_acc": 0.90625, "top5_acc": 1.0, "loss_cls": 0.12358618192374707, "time": 3.2155407905578612, "epoch": 29, "iter": 804, "memory": 30731, "step": 804}
{"base_lr": 0.008622126023955448, "lr": 0.008622126023955448, "data_time": 1.343917679786682, "grad_norm": 3.4175227642059327, "loss": 0.12109743244946003, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.12109743244946003, "time": 2.021162509918213, "epoch": 29, "iter": 812, "memory": 30731, "step": 812}
{"acc/top1": 0.8726166328600405, "acc/top5": 1.0, "acc/mean1": 0.8754145200593809, "data_time": 0.7543216835368763, "time": 0.8355492678555575, "step": 29}
{"base_lr": 0.008506183921362445, "lr": 0.008506183921362445, "data_time": 2.7552199602127074, "grad_norm": 3.3679601550102234, "loss": 0.11776453517377376, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.11776453517377376, "time": 3.429056227207184, "epoch": 30, "iter": 832, "memory": 30731, "step": 832}
{"base_lr": 0.008506183921362445, "lr": 0.008506183921362445, "data_time": 1.8037157893180846, "grad_norm": 3.33763302564621, "loss": 0.11345014050602913, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.11345014050602913, "time": 2.46207218170166, "epoch": 30, "iter": 840, "memory": 30731, "step": 840}
{"acc/top1": 0.8685598377281947, "acc/top5": 1.0, "acc/mean1": 0.8692314152583065, "data_time": 0.5554041428999468, "time": 0.6385033347389915, "step": 30}
{"base_lr": 0.008386407858128709, "lr": 0.008386407858128709, "data_time": 2.2675024390220644, "grad_norm": 3.3831676244735718, "loss": 0.1046595524996519, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.1046595524996519, "time": 3.0828330755233764, "epoch": 31, "iter": 860, "memory": 30731, "step": 860}
{"base_lr": 0.008386407858128709, "lr": 0.008386407858128709, "data_time": 1.3649497389793397, "grad_norm": 3.664299285411835, "loss": 0.10324313417077065, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.10324313417077065, "time": 2.1013647437095644, "epoch": 31, "iter": 868, "memory": 30731, "step": 868}
{"acc/top1": 0.8442190669371197, "acc/top5": 1.0, "acc/mean1": 0.8437176832601102, "data_time": 0.5921634977514093, "time": 0.6720383167266846, "step": 31}
{"base_lr": 0.008262928807620846, "lr": 0.008262928807620846, "data_time": 2.159752130508423, "grad_norm": 3.2031665205955506, "loss": 0.10838389918208122, "top1_acc": 0.9140625, "top5_acc": 1.0, "loss_cls": 0.10838389918208122, "time": 3.119391691684723, "epoch": 32, "iter": 888, "memory": 30731, "step": 888}
{"base_lr": 0.008262928807620846, "lr": 0.008262928807620846, "data_time": 1.235039734840393, "grad_norm": 3.421598994731903, "loss": 0.11312774680554867, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.11312774680554867, "time": 2.086878800392151, "epoch": 32, "iter": 896, "memory": 30731, "step": 896}
{"acc/top1": 0.8709939148073023, "acc/top5": 1.0, "acc/mean1": 0.8744529872307077, "data_time": 0.5949783541939475, "time": 0.6750967719338157, "step": 32}
{"base_lr": 0.008135881792367688, "lr": 0.008135881792367688, "data_time": 2.27327698469162, "grad_norm": 3.6192537903785706, "loss": 0.11418303251266479, "top1_acc": 0.8515625, "top5_acc": 1.0, "loss_cls": 0.11418303251266479, "time": 3.0828839898109437, "epoch": 33, "iter": 916, "memory": 30731, "step": 916}
{"base_lr": 0.008135881792367688, "lr": 0.008135881792367688, "data_time": 1.3609099864959717, "grad_norm": 3.5691264271736145, "loss": 0.11129985861480236, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.11129985861480236, "time": 2.0847076892852785, "epoch": 33, "iter": 924, "memory": 30731, "step": 924}
{"acc/top1": 0.8352941176470589, "acc/top5": 1.0, "acc/mean1": 0.8443606413361594, "data_time": 0.5503120205619119, "time": 0.6292456063357267, "step": 33}
{"base_lr": 0.008005405736415129, "lr": 0.008005405736415129, "data_time": 2.2866446733474732, "grad_norm": 3.379538905620575, "loss": 0.11894671693444252, "top1_acc": 0.9140625, "top5_acc": 1.0, "loss_cls": 0.11894671693444252, "time": 3.104170787334442, "epoch": 34, "iter": 944, "memory": 30731, "step": 944}
{"base_lr": 0.008005405736415129, "lr": 0.008005405736415129, "data_time": 1.3554502964019775, "grad_norm": 3.5042818069458006, "loss": 0.1134202778339386, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.1134202778339386, "time": 2.0771583557128905, "epoch": 34, "iter": 952, "memory": 30731, "step": 952}
{"acc/top1": 0.8158215010141988, "acc/top5": 1.0, "acc/mean1": 0.8252816016897576, "data_time": 0.6148471832275391, "time": 0.6971422542225231, "step": 34}
{"base_lr": 0.00787164331341472, "lr": 0.00787164331341472, "data_time": 2.168360674381256, "grad_norm": 3.2489627480506895, "loss": 0.10168255530297757, "top1_acc": 0.90625, "top5_acc": 1.0, "loss_cls": 0.10168255530297757, "time": 3.099915361404419, "epoch": 35, "iter": 972, "memory": 30731, "step": 972}
{"base_lr": 0.00787164331341472, "lr": 0.00787164331341472, "data_time": 1.2204795241355897, "grad_norm": 3.2383687138557433, "loss": 0.10289134774357081, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.10289134774357081, "time": 2.0554070711135863, "epoch": 35, "iter": 980, "memory": 30731, "step": 980}
{"acc/top1": 0.8681541582150102, "acc/top5": 1.0, "acc/mean1": 0.8723276303169533, "data_time": 0.5398919365622781, "time": 0.619411360133778, "step": 35}
{"base_lr": 0.007734740790612139, "lr": 0.007734740790612139, "data_time": 2.197082507610321, "grad_norm": 3.3206416726112367, "loss": 0.10360799301415682, "top1_acc": 0.9296875, "top5_acc": 1.0, "loss_cls": 0.10360799301415682, "time": 3.074209439754486, "epoch": 36, "iter": 1000, "memory": 30731, "step": 1000}
{"base_lr": 0.007734740790612139, "lr": 0.007734740790612139, "data_time": 1.2613862752914429, "grad_norm": 3.5665594220161436, "loss": 0.09888725001364947, "top1_acc": 0.8333333333333334, "top5_acc": 1.0, "loss_cls": 0.09888725001364947, "time": 2.0648736119270326, "epoch": 36, "iter": 1008, "memory": 30731, "step": 1008}
{"acc/top1": 0.8726166328600405, "acc/top5": 1.0, "acc/mean1": 0.8739113716887582, "data_time": 0.6189645203677091, "time": 0.6986144022508101, "step": 36}
{"base_lr": 0.007594847868906079, "lr": 0.007594847868906079, "data_time": 2.2916823506355284, "grad_norm": 3.3032811284065247, "loss": 0.10145122706890106, "top1_acc": 0.890625, "top5_acc": 1.0, "loss_cls": 0.10145122706890106, "time": 3.106927788257599, "epoch": 37, "iter": 1028, "memory": 30731, "step": 1028}
{"base_lr": 0.007594847868906079, "lr": 0.007594847868906079, "data_time": 1.3428056120872498, "grad_norm": 3.4329106211662292, "loss": 0.10476086400449276, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.10476086400449276, "time": 2.081520450115204, "epoch": 37, "iter": 1036, "memory": 30731, "step": 1036}
{"acc/top1": 0.8559837728194726, "acc/top5": 1.0, "acc/mean1": 0.8549844481243288, "data_time": 0.5263831181959673, "time": 0.6054220199584961, "step": 37}
{"base_lr": 0.007452117519152543, "lr": 0.007452117519152543, "data_time": 2.2289235472679136, "grad_norm": 3.124416196346283, "loss": 0.10219318587332964, "top1_acc": 0.90625, "top5_acc": 1.0, "loss_cls": 0.10219318587332964, "time": 3.0961471796035767, "epoch": 38, "iter": 1056, "memory": 30731, "step": 1056}
{"base_lr": 0.007452117519152543, "lr": 0.007452117519152543, "data_time": 1.2962681531906128, "grad_norm": 3.1836778998374937, "loss": 0.10885952468961477, "top1_acc": 0.7777777777777778, "top5_acc": 1.0, "loss_cls": 0.10885952468961477, "time": 2.095004415512085, "epoch": 38, "iter": 1064, "memory": 30731, "step": 1064}
{"acc/top1": 0.8547667342799189, "acc/top5": 1.0, "acc/mean1": 0.8619649005350082, "data_time": 0.6016875613819469, "time": 0.6832401318983599, "step": 38}
{"base_lr": 0.007306705814893443, "lr": 0.007306705814893443, "data_time": 2.2710102796554565, "grad_norm": 3.332016682624817, "loss": 0.09116321634501219, "top1_acc": 0.9375, "top5_acc": 1.0, "loss_cls": 0.09116321634501219, "time": 3.1136036038398744, "epoch": 39, "iter": 1084, "memory": 30731, "step": 1084}
{"base_lr": 0.007306705814893443, "lr": 0.007306705814893443, "data_time": 1.34097980260849, "grad_norm": 3.522493863105774, "loss": 0.10627328343689442, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.10627328343689442, "time": 2.120704782009125, "epoch": 39, "iter": 1092, "memory": 30731, "step": 1092}
{"acc/top1": 0.8490872210953346, "acc/top5": 1.0, "acc/mean1": 0.8525332049102671, "data_time": 0.5682267492467706, "time": 0.64799579707059, "step": 39}
{"base_lr": 0.007158771761692467, "lr": 0.007158771761692467, "data_time": 2.2544150233268736, "grad_norm": 2.9720722794532777, "loss": 0.0928239667788148, "top1_acc": 0.9453125, "top5_acc": 1.0, "loss_cls": 0.0928239667788148, "time": 3.1244839906692503, "epoch": 40, "iter": 1112, "memory": 30731, "step": 1112}
{"base_lr": 0.007158771761692467, "lr": 0.007158771761692467, "data_time": 1.2803792834281922, "grad_norm": 3.087898850440979, "loss": 0.09628551099449396, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.09628551099449396, "time": 2.08699688911438, "epoch": 40, "iter": 1120, "memory": 30731, "step": 1120}
{"acc/top1": 0.8713995943204869, "acc/top5": 1.0, "acc/mean1": 0.8718673335413064, "data_time": 0.5639897043054755, "time": 0.6436116045171564, "step": 40}
{"base_lr": 0.0070084771232648505, "lr": 0.0070084771232648505, "data_time": 2.3330812215805055, "grad_norm": 3.210489332675934, "loss": 0.08531731646507978, "top1_acc": 0.8828125, "top5_acc": 1.0, "loss_cls": 0.08531731646507978, "time": 3.0981138586997985, "epoch": 41, "iter": 1140, "memory": 30731, "step": 1140}
{"base_lr": 0.0070084771232648505, "lr": 0.0070084771232648505, "data_time": 1.3881985783576964, "grad_norm": 3.280587065219879, "loss": 0.08091303315013647, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.08091303315013647, "time": 2.080351984500885, "epoch": 41, "iter": 1148, "memory": 30731, "step": 1148}
{"acc/top1": 0.8393509127789046, "acc/top5": 1.0, "acc/mean1": 0.8448664708393133, "data_time": 0.5486198555339467, "time": 0.6290839151902632, "step": 41}
{"base_lr": 0.006855986244591106, "lr": 0.006855986244591106, "data_time": 2.2647997736930847, "grad_norm": 3.052176594734192, "loss": 0.08640138693153858, "top1_acc": 0.9453125, "top5_acc": 1.0, "loss_cls": 0.08640138693153858, "time": 3.0783008098602296, "epoch": 42, "iter": 1168, "memory": 30731, "step": 1168}
{"base_lr": 0.006855986244591106, "lr": 0.006855986244591106, "data_time": 1.3510669350624085, "grad_norm": 3.3047383666038512, "loss": 0.08114831019192933, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.08114831019192933, "time": 2.1038689732551576, "epoch": 42, "iter": 1176, "memory": 30731, "step": 1176}
{"acc/top1": 0.8543610547667343, "acc/top5": 1.0, "acc/mean1": 0.8521383794979819, "data_time": 0.5886546915227716, "time": 0.6694671457464044, "step": 42}
{"base_lr": 0.0067014658722082164, "lr": 0.0067014658722082164, "data_time": 2.219121754169464, "grad_norm": 3.218997395038605, "loss": 0.09263363629579544, "top1_acc": 0.8984375, "top5_acc": 1.0, "loss_cls": 0.09263363629579544, "time": 3.116659641265869, "epoch": 43, "iter": 1196, "memory": 30731, "step": 1196}
{"base_lr": 0.0067014658722082164, "lr": 0.0067014658722082164, "data_time": 1.2829712867736816, "grad_norm": 3.4615831851959227, "loss": 0.0948953740298748, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.0948953740298748, "time": 2.0461040854454042, "epoch": 43, "iter": 1204, "memory": 30731, "step": 1204}
{"acc/top1": 0.8661257606490872, "acc/top5": 1.0, "acc/mean1": 0.8696630844278368, "data_time": 0.6014872897755016, "time": 0.6898564208637584, "step": 43}
{"base_lr": 0.006545084971874739, "lr": 0.006545084971874739, "data_time": 2.2217404961586, "grad_norm": 3.1840117812156676, "loss": 0.07281304262578488, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.07281304262578488, "time": 3.0855650067329408, "epoch": 44, "iter": 1224, "memory": 30731, "step": 1224}
{"base_lr": 0.006545084971874739, "lr": 0.006545084971874739, "data_time": 1.3181671977043152, "grad_norm": 3.3822001457214355, "loss": 0.08442383576184512, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.08442383576184512, "time": 2.1007457256317137, "epoch": 44, "iter": 1232, "memory": 30731, "step": 1232}
{"acc/top1": 0.8588235294117647, "acc/top5": 1.0, "acc/mean1": 0.8637858017369116, "data_time": 0.6263822642239657, "time": 0.7065174579620361, "step": 44}
{"base_lr": 0.006387014543809226, "lr": 0.006387014543809226, "data_time": 2.2338958621025085, "grad_norm": 3.049930727481842, "loss": 0.09223914109170436, "top1_acc": 0.859375, "top5_acc": 1.0, "loss_cls": 0.09223914109170436, "time": 3.0595491409301756, "epoch": 45, "iter": 1252, "memory": 30731, "step": 1252}
{"base_lr": 0.006387014543809226, "lr": 0.006387014543809226, "data_time": 1.3331642627716065, "grad_norm": 3.217362070083618, "loss": 0.08497153278440236, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.08497153278440236, "time": 2.0737946033477783, "epoch": 45, "iter": 1260, "memory": 30731, "step": 1260}
{"acc/top1": 0.8689655172413793, "acc/top5": 1.0, "acc/mean1": 0.870348163216638, "data_time": 0.5335383198478005, "time": 0.6129392277110707, "step": 45}
{"base_lr": 0.006227427435703998, "lr": 0.006227427435703998, "data_time": 2.1830370306968687, "grad_norm": 3.0654296398162844, "loss": 0.08374653793871403, "top1_acc": 0.9375, "top5_acc": 1.0, "loss_cls": 0.08374653793871403, "time": 3.1163280725479128, "epoch": 46, "iter": 1280, "memory": 30731, "step": 1280}
{"base_lr": 0.006227427435703998, "lr": 0.006227427435703998, "data_time": 1.2358858704566955, "grad_norm": 3.3301113843917847, "loss": 0.08417358361184597, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.08417358361184597, "time": 2.0472114205360414, "epoch": 46, "iter": 1288, "memory": 30731, "step": 1288}
{"acc/top1": 0.8762677484787018, "acc/top5": 1.0, "acc/mean1": 0.8772852369216904, "data_time": 0.6295493732799183, "time": 0.7096050435846503, "step": 46}
{"base_lr": 0.006066498153718737, "lr": 0.006066498153718737, "data_time": 2.027514934539795, "grad_norm": 3.0190470576286317, "loss": 0.08068417310714722, "top1_acc": 0.953125, "top5_acc": 1.0, "loss_cls": 0.08068417310714722, "time": 2.828217160701752, "epoch": 47, "iter": 1308, "memory": 30731, "step": 1308}
{"base_lr": 0.006066498153718737, "lr": 0.006066498153718737, "data_time": 1.0137986302375794, "grad_norm": 3.024490976333618, "loss": 0.07624153308570385, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.07624153308570385, "time": 1.7331400513648987, "epoch": 47, "iter": 1316, "memory": 30731, "step": 1316}
{"acc/top1": 0.8636916835699797, "acc/top5": 1.0, "acc/mean1": 0.8639564196235386, "data_time": 0.6132778471166437, "time": 0.693577072837136, "step": 47}
{"base_lr": 0.0059044026716605505, "lr": 0.0059044026716605505, "data_time": 2.2821910858154295, "grad_norm": 3.061352014541626, "loss": 0.06976886782795191, "top1_acc": 0.9609375, "top5_acc": 1.0, "loss_cls": 0.06976886782795191, "time": 3.0905654549598696, "epoch": 48, "iter": 1336, "memory": 30731, "step": 1336}
{"base_lr": 0.0059044026716605505, "lr": 0.0059044026716605505, "data_time": 1.3758877635002136, "grad_norm": 3.2307371973991392, "loss": 0.0716415386646986, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.0716415386646986, "time": 2.1029616594314575, "epoch": 48, "iter": 1344, "memory": 30731, "step": 1344}
{"acc/top1": 0.8592292089249493, "acc/top5": 1.0, "acc/mean1": 0.8629103409767334, "data_time": 0.5720326683738015, "time": 0.6539152535525236, "step": 48}
{"base_lr": 0.005741318238559211, "lr": 0.005741318238559211, "data_time": 2.244694638252258, "grad_norm": 3.279413032531738, "loss": 0.07007882967591286, "top1_acc": 0.9296875, "top5_acc": 1.0, "loss_cls": 0.07007882967591286, "time": 3.0607027649879455, "epoch": 49, "iter": 1364, "memory": 30731, "step": 1364}
{"base_lr": 0.005741318238559211, "lr": 0.005741318238559211, "data_time": 1.3178292632102966, "grad_norm": 3.515031433105469, "loss": 0.06738805640488862, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.06738805640488862, "time": 2.078913450241089, "epoch": 49, "iter": 1372, "memory": 30731, "step": 1372}
{"acc/top1": 0.865314401622718, "acc/top5": 1.0, "acc/mean1": 0.865819835349985, "data_time": 0.6285983215678822, "time": 0.7073495821519331, "step": 49}
{"base_lr": 0.005577423184847933, "lr": 0.005577423184847933, "data_time": 2.331872546672821, "grad_norm": 2.9422847867012023, "loss": 0.07646974381059408, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.07646974381059408, "time": 3.061904215812683, "epoch": 50, "iter": 1392, "memory": 30731, "step": 1392}
{"base_lr": 0.005577423184847933, "lr": 0.005577423184847933, "data_time": 1.4268928289413452, "grad_norm": 3.1946337461471557, "loss": 0.07018826492130756, "top1_acc": 0.8703703703703703, "top5_acc": 1.0, "loss_cls": 0.07018826492130756, "time": 2.139620804786682, "epoch": 50, "iter": 1400, "memory": 30731, "step": 1400}
{"acc/top1": 0.8608519269776876, "acc/top5": 1.0, "acc/mean1": 0.8651331090437381, "data_time": 0.5187564546411688, "time": 0.5982517545873468, "step": 50}
{"base_lr": 0.0054128967273616635, "lr": 0.0054128967273616635, "data_time": 2.2243796706199648, "grad_norm": 3.050315499305725, "loss": 0.06459683272987604, "top1_acc": 0.9765625, "top5_acc": 1.0, "loss_cls": 0.06459683272987604, "time": 3.0749545097351074, "epoch": 51, "iter": 1420, "memory": 30731, "step": 1420}
{"base_lr": 0.0054128967273616635, "lr": 0.0054128967273616635, "data_time": 1.2875685930252074, "grad_norm": 3.5354467391967774, "loss": 0.07676439881324768, "top1_acc": 0.8518518518518519, "top5_acc": 1.0, "loss_cls": 0.07676439881324768, "time": 2.0688094019889833, "epoch": 51, "iter": 1428, "memory": 30731, "step": 1428}
{"acc/top1": 0.8531440162271805, "acc/top5": 1.0, "acc/mean1": 0.8524630696638757, "data_time": 0.6104472333734686, "time": 0.6906551881269976, "step": 51}
{"base_lr": 0.005247918773366114, "lr": 0.005247918773366114, "data_time": 2.2890830636024475, "grad_norm": 3.0325244545936583, "loss": 0.06884442903101444, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.06884442903101444, "time": 3.072120487689972, "epoch": 52, "iter": 1448, "memory": 30731, "step": 1448}
{"base_lr": 0.005247918773366114, "lr": 0.005247918773366114, "data_time": 1.3566079616546631, "grad_norm": 3.033308136463165, "loss": 0.07776924222707748, "top1_acc": 0.8518518518518519, "top5_acc": 1.0, "loss_cls": 0.07776924222707748, "time": 2.0950104117393495, "epoch": 52, "iter": 1456, "memory": 30731, "step": 1456}
{"acc/top1": 0.8718052738336713, "acc/top5": 1.0, "acc/mean1": 0.8746912072290959, "data_time": 0.5272212245247581, "time": 0.6063474958593195, "step": 52}
{"base_lr": 0.005082669723831794, "lr": 0.005082669723831794, "data_time": 2.3153344988822937, "grad_norm": 2.870739829540253, "loss": 0.0501617924310267, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.0501617924310267, "time": 3.0887954950332643, "epoch": 53, "iter": 1476, "memory": 30731, "step": 1476}
{"base_lr": 0.005082669723831794, "lr": 0.005082669723831794, "data_time": 1.3742923974990844, "grad_norm": 3.351316714286804, "loss": 0.061815932765603064, "top1_acc": 0.8888888888888888, "top5_acc": 1.0, "loss_cls": 0.061815932765603064, "time": 2.0807130575180053, "epoch": 53, "iter": 1484, "memory": 30731, "step": 1484}
{"acc/top1": 0.8734279918864097, "acc/top5": 1.0, "acc/mean1": 0.8750496739524696, "data_time": 0.6004265221682462, "time": 0.6835204471241344, "step": 53}
{"base_lr": 0.004917330276168209, "lr": 0.004917330276168209, "data_time": 2.3263373970985413, "grad_norm": 2.950752168893814, "loss": 0.06350862057879567, "top1_acc": 0.9453125, "top5_acc": 1.0, "loss_cls": 0.06350862057879567, "time": 3.0947510957717896, "epoch": 54, "iter": 1504, "memory": 30731, "step": 1504}
{"base_lr": 0.004917330276168209, "lr": 0.004917330276168209, "data_time": 1.3916332483291627, "grad_norm": 3.1200455367565154, "loss": 0.05681364862248302, "top1_acc": 0.9259259259259259, "top5_acc": 1.0, "loss_cls": 0.05681364862248302, "time": 2.0898178100585936, "epoch": 54, "iter": 1512, "memory": 30731, "step": 1512}
{"acc/top1": 0.8831643002028398, "acc/top5": 1.0, "acc/mean1": 0.8846553143466501, "data_time": 0.5659177303314209, "time": 0.6451642946763472, "step": 54}
{"base_lr": 0.0047520812266338905, "lr": 0.0047520812266338905, "data_time": 2.0069268941879272, "grad_norm": 2.9045111656188967, "loss": 0.056529607810080054, "top1_acc": 0.9296875, "top5_acc": 1.0, "loss_cls": 0.056529607810080054, "time": 2.862823796272278, "epoch": 55, "iter": 1532, "memory": 30731, "step": 1532}
{"base_lr": 0.0047520812266338905, "lr": 0.0047520812266338905, "data_time": 0.9990387678146362, "grad_norm": 2.9806704044342043, "loss": 0.05924361459910869, "top1_acc": 0.9074074074074074, "top5_acc": 1.0, "loss_cls": 0.05924361459910869, "time": 1.7724833726882934, "epoch": 55, "iter": 1540, "memory": 30731, "step": 1540}
{"acc/top1": 0.8734279918864097, "acc/top5": 1.0, "acc/mean1": 0.8739692822332095, "data_time": 0.6054802157662131, "time": 0.686435114253651, "step": 55}
{"base_lr": 0.00458710327263834, "lr": 0.00458710327263834, "data_time": 2.2435747265815733, "grad_norm": 3.170704883337021, "loss": 0.05558846918866038, "top1_acc": 0.9609375, "top5_acc": 1.0, "loss_cls": 0.05558846918866038, "time": 3.1006267070770264, "epoch": 56, "iter": 1560, "memory": 30731, "step": 1560}
{"base_lr": 0.00458710327263834, "lr": 0.00458710327263834, "data_time": 1.293800413608551, "grad_norm": 3.289507955312729, "loss": 0.051605876814574, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.051605876814574, "time": 2.1011833786964416, "epoch": 56, "iter": 1568, "memory": 30731, "step": 1568}
{"acc/top1": 0.8738336713995943, "acc/top5": 1.0, "acc/mean1": 0.8734179389888094, "data_time": 0.562831618569114, "time": 0.6442715904929421, "step": 56}
{"base_lr": 0.004422576815152072, "lr": 0.004422576815152072, "data_time": 2.2056026101112365, "grad_norm": 3.208865302801132, "loss": 0.05439837798476219, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.05439837798476219, "time": 3.0720420122146606, "epoch": 57, "iter": 1588, "memory": 30731, "step": 1588}
{"base_lr": 0.004422576815152072, "lr": 0.004422576815152072, "data_time": 1.2837229251861573, "grad_norm": 3.224217963218689, "loss": 0.054399846121668816, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.054399846121668816, "time": 2.0866183042526245, "epoch": 57, "iter": 1596, "memory": 30731, "step": 1596}
{"acc/top1": 0.8799188640973631, "acc/top5": 1.0, "acc/mean1": 0.8812553013582106, "data_time": 0.6003141836686567, "time": 0.6828535686839711, "step": 57}
{"base_lr": 0.004258681761440791, "lr": 0.004258681761440791, "data_time": 2.2553779006004335, "grad_norm": 2.9582593858242037, "loss": 0.055900586675852536, "top1_acc": 0.921875, "top5_acc": 1.0, "loss_cls": 0.055900586675852536, "time": 3.1217442989349364, "epoch": 58, "iter": 1616, "memory": 30731, "step": 1616}
{"base_lr": 0.004258681761440791, "lr": 0.004258681761440791, "data_time": 1.3028974294662476, "grad_norm": 3.0378987729549407, "loss": 0.05670421672984958, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.05670421672984958, "time": 2.0896904706954955, "epoch": 58, "iter": 1624, "memory": 30731, "step": 1624}
{"acc/top1": 0.8778904665314402, "acc/top5": 1.0, "acc/mean1": 0.8801343727478027, "data_time": 0.5884900526566939, "time": 0.6684728318994696, "step": 58}
{"base_lr": 0.004095597328339452, "lr": 0.004095597328339452, "data_time": 2.1767518758773803, "grad_norm": 2.726446580886841, "loss": 0.047739464417099954, "top1_acc": 0.9453125, "top5_acc": 1.0, "loss_cls": 0.047739464417099954, "time": 3.1296051263809206, "epoch": 59, "iter": 1644, "memory": 30731, "step": 1644}
{"base_lr": 0.004095597328339452, "lr": 0.004095597328339452, "data_time": 1.1900038838386535, "grad_norm": 2.9340009808540346, "loss": 0.055554453656077384, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.055554453656077384, "time": 2.0534668564796448, "epoch": 59, "iter": 1652, "memory": 30731, "step": 1652}
{"acc/top1": 0.8766734279918864, "acc/top5": 1.0, "acc/mean1": 0.8770533111748974, "data_time": 0.5721677866849032, "time": 0.6525437398390337, "step": 59}
{"base_lr": 0.003933501846281268, "lr": 0.003933501846281268, "data_time": 2.239883852005005, "grad_norm": 2.7608192324638368, "loss": 0.046093819476664064, "top1_acc": 0.96875, "top5_acc": 1.0, "loss_cls": 0.046093819476664064, "time": 3.0862383604049684, "epoch": 60, "iter": 1672, "memory": 30731, "step": 1672}
{"base_lr": 0.003933501846281268, "lr": 0.003933501846281268, "data_time": 1.2989770412445067, "grad_norm": 3.003450262546539, "loss": 0.04957814207300544, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.04957814207300544, "time": 2.096901595592499, "epoch": 60, "iter": 1680, "memory": 30731, "step": 1680}
{"acc/top1": 0.8758620689655172, "acc/top5": 1.0, "acc/mean1": 0.8765731780442185, "data_time": 0.5646693706512451, "time": 0.64527043429288, "step": 60}
{"base_lr": 0.0037725725642960063, "lr": 0.0037725725642960063, "data_time": 2.2817490696907043, "grad_norm": 3.1927672266960143, "loss": 0.051412496529519555, "top1_acc": 0.953125, "top5_acc": 1.0, "loss_cls": 0.051412496529519555, "time": 3.0594738364219665, "epoch": 61, "iter": 1700, "memory": 30731, "step": 1700}
{"base_lr": 0.0037725725642960063, "lr": 0.0037725725642960063, "data_time": 1.3867130875587463, "grad_norm": 3.210531234741211, "loss": 0.047765052504837514, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.047765052504837514, "time": 2.1232006907463075, "epoch": 61, "iter": 1708, "memory": 30731, "step": 1708}
{"acc/top1": 0.8787018255578093, "acc/top5": 1.0, "acc/mean1": 0.8815584905027464, "data_time": 0.5618264458396218, "time": 0.6421869451349432, "step": 61}
{"base_lr": 0.0036129854561907796, "lr": 0.0036129854561907796, "data_time": 2.2747987031936647, "grad_norm": 2.5905427753925325, "loss": 0.04034812096506357, "top1_acc": 0.9609375, "top5_acc": 1.0, "loss_cls": 0.04034812096506357, "time": 3.0916581988334655, "epoch": 62, "iter": 1728, "memory": 30731, "step": 1728}
{"base_lr": 0.0036129854561907796, "lr": 0.0036129854561907796, "data_time": 1.357911479473114, "grad_norm": 2.7968390941619874, "loss": 0.04627482509240508, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.04627482509240508, "time": 2.0813839435577393, "epoch": 62, "iter": 1736, "memory": 30731, "step": 1736}
{"acc/top1": 0.8807302231237323, "acc/top5": 1.0, "acc/mean1": 0.8814801538042668, "data_time": 0.6127377856861461, "time": 0.6927127621390603, "step": 62}
{"base_lr": 0.003454915028125266, "lr": 0.003454915028125266, "data_time": 2.330224943161011, "grad_norm": 2.6385786533355713, "loss": 0.03346482506021857, "top1_acc": 0.984375, "top5_acc": 1.0, "loss_cls": 0.03346482506021857, "time": 3.084244990348816, "epoch": 63, "iter": 1756, "memory": 30731, "step": 1756}
{"base_lr": 0.003454915028125266, "lr": 0.003454915028125266, "data_time": 1.3910600185394286, "grad_norm": 2.886779272556305, "loss": 0.033914197236299515, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.033914197236299515, "time": 2.1249812483787536, "epoch": 63, "iter": 1764, "memory": 30731, "step": 1764}
{"acc/top1": 0.8774847870182556, "acc/top5": 1.0, "acc/mean1": 0.8772600960504509, "data_time": 0.5852821090004661, "time": 0.6674603765661066, "step": 63}
{"base_lr": 0.003298534127791786, "lr": 0.003298534127791786, "data_time": 2.295867848396301, "grad_norm": 2.9382283866405485, "loss": 0.04320499817840755, "top1_acc": 0.9296875, "top5_acc": 1.0, "loss_cls": 0.04320499817840755, "time": 3.1096983909606934, "epoch": 64, "iter": 1784, "memory": 30731, "step": 1784}
{"base_lr": 0.003298534127791786, "lr": 0.003298534127791786, "data_time": 1.3491519331932067, "grad_norm": 3.0183826863765715, "loss": 0.04368147691711784, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.04368147691711784, "time": 2.082465672492981, "epoch": 64, "iter": 1792, "memory": 30731, "step": 1792}
{"acc/top1": 0.8880324543610547, "acc/top5": 1.0, "acc/mean1": 0.8905131416326744, "data_time": 0.5659612959081476, "time": 0.6471256776289507, "step": 64}
{"base_lr": 0.0031440137554088975, "lr": 0.0031440137554088975, "data_time": 2.164456009864807, "grad_norm": 2.915219080448151, "loss": 0.03969236258417368, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.03969236258417368, "time": 2.8490984201431275, "epoch": 65, "iter": 1812, "memory": 30731, "step": 1812}
{"base_lr": 0.0031440137554088975, "lr": 0.0031440137554088975, "data_time": 1.145960521697998, "grad_norm": 2.8221815705299376, "loss": 0.040381244570016864, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.040381244570016864, "time": 1.7898320317268372, "epoch": 65, "iter": 1820, "memory": 30731, "step": 1820}
{"acc/top1": 0.8835699797160244, "acc/top5": 1.0, "acc/mean1": 0.8854147090339191, "data_time": 0.5607238249345259, "time": 0.6492661779577081, "step": 65}
{"base_lr": 0.0029915228767351545, "lr": 0.0029915228767351545, "data_time": 2.151472342014313, "grad_norm": 3.3522092580795286, "loss": 0.04458777904510498, "top1_acc": 0.9765625, "top5_acc": 1.0, "loss_cls": 0.04458777904510498, "time": 3.1185958623886108, "epoch": 66, "iter": 1840, "memory": 30731, "step": 1840}
{"base_lr": 0.0029915228767351545, "lr": 0.0029915228767351545, "data_time": 1.210723626613617, "grad_norm": 3.1581614017486572, "loss": 0.0479694084264338, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.0479694084264338, "time": 2.041755795478821, "epoch": 66, "iter": 1848, "memory": 30731, "step": 1848}
{"acc/top1": 0.8835699797160244, "acc/top5": 1.0, "acc/mean1": 0.8846687351830035, "data_time": 0.6132674433968284, "time": 0.6926898956298828, "step": 66}
{"base_lr": 0.002841228238307537, "lr": 0.002841228238307537, "data_time": 2.2508233428001403, "grad_norm": 2.8948313355445863, "loss": 0.04026433494873345, "top1_acc": 0.9140625, "top5_acc": 1.0, "loss_cls": 0.04026433494873345, "time": 3.099429559707642, "epoch": 67, "iter": 1868, "memory": 30731, "step": 1868}
{"base_lr": 0.002841228238307537, "lr": 0.002841228238307537, "data_time": 1.3335214257240295, "grad_norm": 2.9291747212409973, "loss": 0.03455299264751375, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.03455299264751375, "time": 2.102249550819397, "epoch": 67, "iter": 1876, "memory": 30731, "step": 1876}
{"acc/top1": 0.8827586206896552, "acc/top5": 1.0, "acc/mean1": 0.8823320679266299, "data_time": 0.5557685982097279, "time": 0.6381576928225431, "step": 67}
{"base_lr": 0.0026932941851065626, "lr": 0.0026932941851065626, "data_time": 2.2621750593185426, "grad_norm": 2.6626265406608582, "loss": 0.03555527366697788, "top1_acc": 0.96875, "top5_acc": 1.0, "loss_cls": 0.03555527366697788, "time": 3.1157470464706423, "epoch": 68, "iter": 1896, "memory": 30731, "step": 1896}
{"base_lr": 0.0026932941851065626, "lr": 0.0026932941851065626, "data_time": 1.3195564508438111, "grad_norm": 2.7764453172683714, "loss": 0.03419214086607099, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.03419214086607099, "time": 2.1353676676750184, "epoch": 68, "iter": 1904, "memory": 30731, "step": 1904}
{"acc/top1": 0.877079107505071, "acc/top5": 1.0, "acc/mean1": 0.8778157730619317, "data_time": 0.5719438249414618, "time": 0.6620465842160311, "step": 68}
{"base_lr": 0.0025478824808474604, "lr": 0.0025478824808474604, "data_time": 2.3192341685295106, "grad_norm": 2.5174876034259794, "loss": 0.0334479249548167, "top1_acc": 0.984375, "top5_acc": 1.0, "loss_cls": 0.0334479249548167, "time": 3.1418735861778258, "epoch": 69, "iter": 1924, "memory": 30731, "step": 1924}
{"base_lr": 0.0025478824808474604, "lr": 0.0025478824808474604, "data_time": 1.3482690930366517, "grad_norm": 2.71751047372818, "loss": 0.033681975398212674, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.033681975398212674, "time": 2.0917391777038574, "epoch": 69, "iter": 1932, "memory": 30731, "step": 1932}
{"acc/top1": 0.8896551724137931, "acc/top5": 1.0, "acc/mean1": 0.8905440573894144, "data_time": 0.6024347218600187, "time": 0.6815649812871759, "step": 69}
{"base_lr": 0.002405152131093925, "lr": 0.002405152131093925, "data_time": 2.041537845134735, "grad_norm": 2.657085341215134, "loss": 0.026693719532340766, "top1_acc": 0.96875, "top5_acc": 1.0, "loss_cls": 0.026693719532340766, "time": 2.821300446987152, "epoch": 70, "iter": 1952, "memory": 30731, "step": 1952}
{"base_lr": 0.002405152131093925, "lr": 0.002405152131093925, "data_time": 1.0567075610160828, "grad_norm": 2.9525763392448425, "loss": 0.031617744080722335, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.031617744080722335, "time": 1.725724959373474, "epoch": 70, "iter": 1960, "memory": 30731, "step": 1960}
{"acc/top1": 0.8823529411764706, "acc/top5": 1.0, "acc/mean1": 0.8848987537293035, "data_time": 0.6055846864526923, "time": 0.6846352057023481, "step": 70}
{"base_lr": 0.0022652592093878677, "lr": 0.0022652592093878677, "data_time": 2.295011818408966, "grad_norm": 2.6401393175125123, "loss": 0.03001472884789109, "top1_acc": 0.953125, "top5_acc": 1.0, "loss_cls": 0.03001472884789109, "time": 3.0905072569847105, "epoch": 71, "iter": 1980, "memory": 30731, "step": 1980}
{"base_lr": 0.0022652592093878677, "lr": 0.0022652592093878677, "data_time": 1.3903733968734742, "grad_norm": 2.592601352930069, "loss": 0.02699243533425033, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.02699243533425033, "time": 2.12338045835495, "epoch": 71, "iter": 1988, "memory": 30731, "step": 1988}
{"acc/top1": 0.8855983772819472, "acc/top5": 1.0, "acc/mean1": 0.8857435945509299, "data_time": 0.6082721623507413, "time": 0.6889438412406228, "step": 71}
{"base_lr": 0.002128356686585283, "lr": 0.002128356686585283, "data_time": 2.290672469139099, "grad_norm": 2.6797121286392214, "loss": 0.027520592650398613, "top1_acc": 0.96875, "top5_acc": 1.0, "loss_cls": 0.027520592650398613, "time": 3.2070489287376405, "epoch": 72, "iter": 2008, "memory": 30731, "step": 2008}
{"base_lr": 0.002128356686585283, "lr": 0.002128356686585283, "data_time": 1.2932090759277344, "grad_norm": 2.772244209051132, "loss": 0.02957196799106896, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.02957196799106896, "time": 2.132338893413544, "epoch": 72, "iter": 2016, "memory": 30731, "step": 2016}
{"acc/top1": 0.8888438133874239, "acc/top5": 1.0, "acc/mean1": 0.8906928251524736, "data_time": 0.5700917894190008, "time": 0.6523407155817206, "step": 72}
{"base_lr": 0.0019945942635848754, "lr": 0.0019945942635848754, "data_time": 2.2674625635147097, "grad_norm": 2.529217094182968, "loss": 0.02675478751771152, "top1_acc": 0.9375, "top5_acc": 1.0, "loss_cls": 0.02675478751771152, "time": 3.1256155252456663, "epoch": 73, "iter": 2036, "memory": 30731, "step": 2036}
{"base_lr": 0.0019945942635848754, "lr": 0.0019945942635848754, "data_time": 1.3112340211868285, "grad_norm": 2.973109519481659, "loss": 0.036599361011758445, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.036599361011758445, "time": 2.094122838973999, "epoch": 73, "iter": 2044, "memory": 30731, "step": 2044}
{"acc/top1": 0.8864097363083164, "acc/top5": 1.0, "acc/mean1": 0.8868009589984691, "data_time": 0.5674705938859419, "time": 0.6519263874400746, "step": 73}
{"base_lr": 0.0018641182076323157, "lr": 0.0018641182076323157, "data_time": 2.2363646984100343, "grad_norm": 2.4368872225284575, "loss": 0.025331968790851533, "top1_acc": 0.9765625, "top5_acc": 1.0, "loss_cls": 0.025331968790851533, "time": 3.1252716898918154, "epoch": 74, "iter": 2064, "memory": 30731, "step": 2064}
{"base_lr": 0.0018641182076323157, "lr": 0.0018641182076323157, "data_time": 1.2790425658226012, "grad_norm": 2.7079445481300355, "loss": 0.024648810690268873, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.024648810690268873, "time": 2.091158127784729, "epoch": 74, "iter": 2072, "memory": 30731, "step": 2072}
{"acc/top1": 0.8900608519269777, "acc/top5": 1.0, "acc/mean1": 0.8906900856188175, "data_time": 0.5188918980685148, "time": 0.6000440987673673, "step": 74}
{"base_lr": 0.0017370711923791575, "lr": 0.0017370711923791575, "data_time": 2.071243739128113, "grad_norm": 2.3182888507843016, "loss": 0.019235847005620598, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.019235847005620598, "time": 2.824193072319031, "epoch": 75, "iter": 2092, "memory": 30731, "step": 2092}
{"base_lr": 0.0017370711923791575, "lr": 0.0017370711923791575, "data_time": 1.0548543453216552, "grad_norm": 2.362667953968048, "loss": 0.018086338019929826, "top1_acc": 0.9444444444444444, "top5_acc": 1.0, "loss_cls": 0.018086338019929826, "time": 1.7621087431907654, "epoch": 75, "iter": 2100, "memory": 30731, "step": 2100}
{"acc/top1": 0.8835699797160244, "acc/top5": 1.0, "acc/mean1": 0.8828561034162502, "data_time": 0.5845736156810414, "time": 0.6653084104711359, "step": 75}
{"base_lr": 0.0016135921418712965, "lr": 0.0016135921418712965, "data_time": 2.249842405319214, "grad_norm": 2.006917104125023, "loss": 0.018876215489581228, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.018876215489581228, "time": 3.0857683539390566, "epoch": 76, "iter": 2120, "memory": 30731, "step": 2120}
{"base_lr": 0.0016135921418712965, "lr": 0.0016135921418712965, "data_time": 1.3213905692100525, "grad_norm": 2.102720448374748, "loss": 0.021564281196333468, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.021564281196333468, "time": 2.0666245102882383, "epoch": 76, "iter": 2128, "memory": 30731, "step": 2128}
{"acc/top1": 0.8876267748478702, "acc/top5": 1.0, "acc/mean1": 0.8890202403305968, "data_time": 0.6185842644084584, "time": 0.7001083764162931, "step": 76}
{"base_lr": 0.0014938160786375579, "lr": 0.0014938160786375579, "data_time": 2.2785339951515198, "grad_norm": 2.3650608360767365, "loss": 0.014299063547514378, "top1_acc": 0.984375, "top5_acc": 1.0, "loss_cls": 0.014299063547514378, "time": 3.0982685804367067, "epoch": 77, "iter": 2148, "memory": 30731, "step": 2148}
{"base_lr": 0.0014938160786375579, "lr": 0.0014938160786375579, "data_time": 1.3280289769172668, "grad_norm": 2.705630588531494, "loss": 0.014990120357833803, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.014990120357833803, "time": 2.110394549369812, "epoch": 77, "iter": 2156, "memory": 30731, "step": 2156}
{"acc/top1": 0.8929006085192698, "acc/top5": 1.0, "acc/mean1": 0.8934400096592294, "data_time": 0.5459179878234863, "time": 0.6259150938554243, "step": 77}
{"base_lr": 0.0013778739760445559, "lr": 0.0013778739760445559, "data_time": 2.1188273191452027, "grad_norm": 2.2722347736358643, "loss": 0.026941518299281597, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.026941518299281597, "time": 2.8667358279228212, "epoch": 78, "iter": 2176, "memory": 30731, "step": 2176}
{"base_lr": 0.0013778739760445559, "lr": 0.0013778739760445559, "data_time": 1.1110522985458373, "grad_norm": 2.2893724262714388, "loss": 0.02500097779557109, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.02500097779557109, "time": 1.8280722737312316, "epoch": 78, "iter": 2184, "memory": 30731, "step": 2184}
{"acc/top1": 0.8835699797160244, "acc/top5": 1.0, "acc/mean1": 0.8846304331584579, "data_time": 0.5729925849220969, "time": 0.6536212617700751, "step": 78}
{"base_lr": 0.0012658926150792328, "lr": 0.0012658926150792328, "data_time": 2.1058375358581545, "grad_norm": 2.0700282126665117, "loss": 0.013573575904592872, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.013573575904592872, "time": 3.0808449387550354, "epoch": 79, "iter": 2204, "memory": 30731, "step": 2204}
{"base_lr": 0.0012658926150792328, "lr": 0.0012658926150792328, "data_time": 1.1676341056823731, "grad_norm": 2.1752363413572313, "loss": 0.014739813725464046, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.014739813725464046, "time": 2.031968724727631, "epoch": 79, "iter": 2212, "memory": 30731, "step": 2212}
{"acc/top1": 0.8884381338742393, "acc/top5": 1.0, "acc/mean1": 0.8883157422720354, "data_time": 0.5304651477120139, "time": 0.6092318621548739, "step": 79}
{"base_lr": 0.0011579944457157049, "lr": 0.0011579944457157049, "data_time": 2.1988576889038085, "grad_norm": 2.2621346384286882, "loss": 0.015465791360475123, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.015465791360475123, "time": 3.140453028678894, "epoch": 80, "iter": 2232, "memory": 30731, "step": 2232}
{"base_lr": 0.0011579944457157049, "lr": 0.0011579944457157049, "data_time": 1.2715011954307556, "grad_norm": 2.1835096031427383, "loss": 0.012051819078624248, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.012051819078624248, "time": 2.0896848678588866, "epoch": 80, "iter": 2240, "memory": 30731, "step": 2240}
{"acc/top1": 0.8855983772819472, "acc/top5": 1.0, "acc/mean1": 0.8872342756854454, "data_time": 0.6168492924083363, "time": 0.695217024196278, "step": 80}
{"base_lr": 0.0010542974530180333, "lr": 0.0010542974530180333, "data_time": 2.2657810926437376, "grad_norm": 2.4938387125730515, "loss": 0.019010017917025834, "top1_acc": 0.96875, "top5_acc": 1.0, "loss_cls": 0.019010017917025834, "time": 3.1529179573059083, "epoch": 81, "iter": 2260, "memory": 30731, "step": 2260}
{"base_lr": 0.0010542974530180333, "lr": 0.0010542974530180333, "data_time": 1.3178869366645813, "grad_norm": 2.3021961212158204, "loss": 0.01726492290617898, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.01726492290617898, "time": 2.092119777202606, "epoch": 81, "iter": 2268, "memory": 30731, "step": 2268}
{"acc/top1": 0.8855983772819472, "acc/top5": 1.0, "acc/mean1": 0.8857706959066838, "data_time": 0.6300787925720215, "time": 0.7108024467121471, "step": 81}
{"base_lr": 0.0009549150281252639, "lr": 0.0009549150281252639, "data_time": 2.2976704597473145, "grad_norm": 2.0046502321958544, "loss": 0.018398549512494356, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.018398549512494356, "time": 3.1535022854804993, "epoch": 82, "iter": 2288, "memory": 30731, "step": 2288}
{"base_lr": 0.0009549150281252639, "lr": 0.0009549150281252639, "data_time": 1.3868464350700378, "grad_norm": 2.161726397275925, "loss": 0.017585879913531242, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.017585879913531242, "time": 2.1520153284072876, "epoch": 82, "iter": 2296, "memory": 30731, "step": 2296}
{"acc/top1": 0.8791075050709939, "acc/top5": 1.0, "acc/mean1": 0.8814423711458156, "data_time": 0.6216631585901434, "time": 0.7017022262920033, "step": 82}
{"base_lr": 0.0008599558442599003, "lr": 0.0008599558442599003, "data_time": 2.3868942618370057, "grad_norm": 2.1379029870033266, "loss": 0.013510344095993786, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.013510344095993786, "time": 3.192454791069031, "epoch": 83, "iter": 2316, "memory": 30731, "step": 2316}
{"base_lr": 0.0008599558442599003, "lr": 0.0008599558442599003, "data_time": 1.4243051767349244, "grad_norm": 1.9751983880996704, "loss": 0.012805219017900526, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.012805219017900526, "time": 2.1798545598983763, "epoch": 83, "iter": 2324, "memory": 30731, "step": 2324}
{"acc/top1": 0.8900608519269777, "acc/top5": 1.0, "acc/mean1": 0.8907159204072563, "data_time": 0.6149586330760609, "time": 0.6946366266770796, "step": 83}
{"base_lr": 0.0007695237378953239, "lr": 0.0007695237378953239, "data_time": 2.3110984921455384, "grad_norm": 2.167366456985474, "loss": 0.02058511204086244, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.02058511204086244, "time": 3.1727512001991274, "epoch": 84, "iter": 2344, "memory": 30731, "step": 2344}
{"base_lr": 0.0007695237378953239, "lr": 0.0007695237378953239, "data_time": 1.3534451961517333, "grad_norm": 2.2748598158359528, "loss": 0.021282491413876413, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.021282491413876413, "time": 2.160176682472229, "epoch": 84, "iter": 2352, "memory": 30731, "step": 2352}
{"acc/top1": 0.8896551724137931, "acc/top5": 1.0, "acc/mean1": 0.8908410735494384, "data_time": 0.6223243583332408, "time": 0.702687068419023, "step": 84}
{"base_lr": 0.0006837175952121308, "lr": 0.0006837175952121308, "data_time": 2.3021937251091003, "grad_norm": 1.7207495450973511, "loss": 0.010069953696802258, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.010069953696802258, "time": 3.1371891498565674, "epoch": 85, "iter": 2372, "memory": 30731, "step": 2372}
{"base_lr": 0.0006837175952121308, "lr": 0.0006837175952121308, "data_time": 1.4151689648628234, "grad_norm": 1.8525747805833817, "loss": 0.013592287874780595, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.013592287874780595, "time": 2.1723902583122254, "epoch": 85, "iter": 2380, "memory": 30731, "step": 2380}
{"acc/top1": 0.8892494929006085, "acc/top5": 1.0, "acc/mean1": 0.890050243739335, "data_time": 0.6079549355940386, "time": 0.6899546059695157, "step": 85}
{"base_lr": 0.0006026312439675554, "lr": 0.0006026312439675554, "data_time": 2.2837574243545533, "grad_norm": 1.7706698432564736, "loss": 0.01442259302129969, "top1_acc": 0.984375, "top5_acc": 1.0, "loss_cls": 0.01442259302129969, "time": 3.1278811573982237, "epoch": 86, "iter": 2400, "memory": 30731, "step": 2400}
{"base_lr": 0.0006026312439675554, "lr": 0.0006026312439675554, "data_time": 1.367259418964386, "grad_norm": 1.9066664308309555, "loss": 0.011974017787724733, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.011974017787724733, "time": 2.1134001851081847, "epoch": 86, "iter": 2408, "memory": 30731, "step": 2408}
{"acc/top1": 0.8904665314401623, "acc/top5": 1.0, "acc/mean1": 0.8924158662679343, "data_time": 0.6205176656896417, "time": 0.6997768228704279, "step": 86}
{"base_lr": 0.0005263533508961828, "lr": 0.0005263533508961828, "data_time": 2.2944401025772097, "grad_norm": 1.8229891538619996, "loss": 0.012835837411694228, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.012835837411694228, "time": 3.108420717716217, "epoch": 87, "iter": 2428, "memory": 30731, "step": 2428}
{"base_lr": 0.0005263533508961828, "lr": 0.0005263533508961828, "data_time": 1.3680458188056945, "grad_norm": 2.0427720308303834, "loss": 0.012069281202275305, "top1_acc": 0.9629629629629629, "top5_acc": 1.0, "loss_cls": 0.012069281202275305, "time": 2.1736673712730408, "epoch": 87, "iter": 2436, "memory": 30731, "step": 2436}
{"acc/top1": 0.8908722109533469, "acc/top5": 1.0, "acc/mean1": 0.8909575738564999, "data_time": 0.6465443914586847, "time": 0.7270951054312966, "step": 87}
{"base_lr": 0.0004549673247541887, "lr": 0.0004549673247541887, "data_time": 2.3659210562705995, "grad_norm": 1.8492593228816987, "loss": 0.014209647383540868, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.014209647383540868, "time": 3.1406426429748535, "epoch": 88, "iter": 2456, "memory": 30731, "step": 2456}
{"base_lr": 0.0004549673247541887, "lr": 0.0004549673247541887, "data_time": 1.4504391551017761, "grad_norm": 2.220134896039963, "loss": 0.01200310019776225, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.01200310019776225, "time": 2.1901710391044618, "epoch": 88, "iter": 2464, "memory": 30731, "step": 2464}
{"acc/top1": 0.8880324543610547, "acc/top5": 1.0, "acc/mean1": 0.8896995975179188, "data_time": 0.707307598807595, "time": 0.7859161116860129, "step": 88}
{"base_lr": 0.00038855122511307635, "lr": 0.00038855122511307635, "data_time": 2.2259435057640076, "grad_norm": 1.9842216730117799, "loss": 0.017170340393204243, "top1_acc": 0.984375, "top5_acc": 1.0, "loss_cls": 0.017170340393204243, "time": 3.1798357248306273, "epoch": 89, "iter": 2484, "memory": 30731, "step": 2484}
{"base_lr": 0.00038855122511307635, "lr": 0.00038855122511307635, "data_time": 1.261092734336853, "grad_norm": 2.322029522061348, "loss": 0.025860113126691432, "top1_acc": 0.8703703703703703, "top5_acc": 1.0, "loss_cls": 0.025860113126691432, "time": 2.1580862045288085, "epoch": 89, "iter": 2492, "memory": 30731, "step": 2492}
{"acc/top1": 0.8795131845841785, "acc/top5": 1.0, "acc/mean1": 0.8805221077104708, "data_time": 0.6700153567574241, "time": 0.7491047165610574, "step": 89}
{"base_lr": 0.0003271776770026953, "lr": 0.0003271776770026953, "data_time": 2.311400902271271, "grad_norm": 1.6553445339202881, "loss": 0.007075851853005588, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.007075851853005588, "time": 3.1929192304611207, "epoch": 90, "iter": 2512, "memory": 30731, "step": 2512}
{"base_lr": 0.0003271776770026953, "lr": 0.0003271776770026953, "data_time": 1.3687349796295165, "grad_norm": 1.8439615786075592, "loss": 0.008674890187103301, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.008674890187103301, "time": 2.152611517906189, "epoch": 90, "iter": 2520, "memory": 30731, "step": 2520}
{"acc/top1": 0.8884381338742393, "acc/top5": 1.0, "acc/mean1": 0.8894508707679384, "data_time": 0.6747014739296653, "time": 0.7543170235373757, "step": 90}
{"base_lr": 0.0002709137914968269, "lr": 0.0002709137914968269, "data_time": 2.368425488471985, "grad_norm": 1.7982907354831696, "loss": 0.016674843640066682, "top1_acc": 0.984375, "top5_acc": 1.0, "loss_cls": 0.016674843640066682, "time": 3.199133777618408, "epoch": 91, "iter": 2540, "memory": 30731, "step": 2540}
{"base_lr": 0.0002709137914968269, "lr": 0.0002709137914968269, "data_time": 1.4247660279273986, "grad_norm": 1.9313454270362853, "loss": 0.016977902548387647, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.016977902548387647, "time": 2.1784353733062742, "epoch": 91, "iter": 2548, "memory": 30731, "step": 2548}
{"acc/top1": 0.8888438133874239, "acc/top5": 1.0, "acc/mean1": 0.8894957267271265, "data_time": 0.6736198121851141, "time": 0.7536131685430353, "step": 91}
{"base_lr": 0.00021982109232821187, "lr": 0.00021982109232821187, "data_time": 2.3390366077423095, "grad_norm": 1.936487302184105, "loss": 0.011256178608164191, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.011256178608164191, "time": 3.1250259399414064, "epoch": 92, "iter": 2568, "memory": 30731, "step": 2568}
{"base_lr": 0.00021982109232821187, "lr": 0.00021982109232821187, "data_time": 1.4340384602546692, "grad_norm": 1.798013946413994, "loss": 0.0076504192315042015, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.0076504192315042015, "time": 2.1663724422454833, "epoch": 92, "iter": 2576, "memory": 30731, "step": 2576}
{"acc/top1": 0.8896551724137931, "acc/top5": 1.0, "acc/mean1": 0.8908174056445399, "data_time": 0.5965853821147572, "time": 0.6758669072931464, "step": 92}
{"base_lr": 0.00017395544861325727, "lr": 0.00017395544861325727, "data_time": 2.3217299342155457, "grad_norm": 2.0087082892656327, "loss": 0.01390161165036261, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.01390161165036261, "time": 3.131329560279846, "epoch": 93, "iter": 2596, "memory": 30731, "step": 2596}
{"base_lr": 0.00017395544861325727, "lr": 0.00017395544861325727, "data_time": 1.4216859817504883, "grad_norm": 1.768510925769806, "loss": 0.014897166239097714, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.014897166239097714, "time": 2.1694421768188477, "epoch": 93, "iter": 2604, "memory": 30731, "step": 2604}
{"acc/top1": 0.8904665314401623, "acc/top5": 1.0, "acc/mean1": 0.8916642920826231, "data_time": 0.6882188970392401, "time": 0.76887432011691, "step": 93}
{"base_lr": 0.00013336701375997136, "lr": 0.00013336701375997136, "data_time": 2.320565605163574, "grad_norm": 1.7561598479747773, "loss": 0.009096586517989636, "top1_acc": 0.9765625, "top5_acc": 1.0, "loss_cls": 0.009096586517989636, "time": 3.154673528671265, "epoch": 94, "iter": 2624, "memory": 30731, "step": 2624}
{"base_lr": 0.00013336701375997136, "lr": 0.00013336701375997136, "data_time": 1.4224795341491698, "grad_norm": 1.9174897164106368, "loss": 0.010999807622283697, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.010999807622283697, "time": 2.191133368015289, "epoch": 94, "iter": 2632, "memory": 30731, "step": 2632}
{"acc/top1": 0.8880324543610547, "acc/top5": 1.0, "acc/mean1": 0.8896096259164955, "data_time": 0.6718418164686724, "time": 0.7508621215820312, "step": 94}
{"base_lr": 9.810017062595328e-05, "lr": 9.810017062595328e-05, "data_time": 2.387558376789093, "grad_norm": 1.9672526925802232, "loss": 0.01813423555577174, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.01813423555577174, "time": 3.1382635116577147, "epoch": 95, "iter": 2652, "memory": 30731, "step": 2652}
{"base_lr": 9.810017062595328e-05, "lr": 9.810017062595328e-05, "data_time": 1.4924187779426574, "grad_norm": 2.120601013302803, "loss": 0.016893683932721615, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.016893683932721615, "time": 2.208782768249512, "epoch": 95, "iter": 2660, "memory": 30731, "step": 2660}
{"acc/top1": 0.8872210953346856, "acc/top5": 1.0, "acc/mean1": 0.8876151437464025, "data_time": 0.6734754172238436, "time": 0.7518602067773993, "step": 95}
{"base_lr": 6.819348298638844e-05, "lr": 6.819348298638844e-05, "data_time": 2.2734040141105654, "grad_norm": 1.6721468806266784, "loss": 0.016664935054723173, "top1_acc": 0.9921875, "top5_acc": 1.0, "loss_cls": 0.016664935054723173, "time": 3.178984522819519, "epoch": 96, "iter": 2680, "memory": 30731, "step": 2680}
{"base_lr": 6.819348298638844e-05, "lr": 6.819348298638844e-05, "data_time": 1.319899046421051, "grad_norm": 1.715969780087471, "loss": 0.01674460241920315, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.01674460241920315, "time": 2.1460414290428163, "epoch": 96, "iter": 2688, "memory": 30731, "step": 2688}
{"acc/top1": 0.8884381338742393, "acc/top5": 1.0, "acc/mean1": 0.8890953181293257, "data_time": 0.6718640760941939, "time": 0.7520599365234375, "step": 96}
{"base_lr": 4.3679653365124054e-05, "lr": 4.3679653365124054e-05, "data_time": 2.3224684834480285, "grad_norm": 1.687574142217636, "loss": 0.00820355931064114, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.00820355931064114, "time": 3.171586203575134, "epoch": 97, "iter": 2708, "memory": 30731, "step": 2708}
{"base_lr": 4.3679653365124054e-05, "lr": 4.3679653365124054e-05, "data_time": 1.3864909291267395, "grad_norm": 1.8145524740219117, "loss": 0.012363100436050444, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.012363100436050444, "time": 2.1574665546417235, "epoch": 97, "iter": 2716, "memory": 30731, "step": 2716}
{"acc/top1": 0.8920892494929006, "acc/top5": 1.0, "acc/mean1": 0.8930408143447225, "data_time": 0.6532694426449862, "time": 0.7330491326072, "step": 97}
{"base_lr": 2.4585487274942935e-05, "lr": 2.4585487274942935e-05, "data_time": 2.234817373752594, "grad_norm": 1.6620729207992553, "loss": 0.009061258402653039, "top1_acc": 0.9765625, "top5_acc": 1.0, "loss_cls": 0.009061258402653039, "time": 3.1414154171943665, "epoch": 98, "iter": 2736, "memory": 30731, "step": 2736}
{"base_lr": 2.4585487274942935e-05, "lr": 2.4585487274942935e-05, "data_time": 1.3067052006721496, "grad_norm": 1.7526908308267592, "loss": 0.00888555720448494, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.00888555720448494, "time": 2.1157339453697204, "epoch": 98, "iter": 2744, "memory": 30731, "step": 2744}
{"acc/top1": 0.8900608519269777, "acc/top5": 1.0, "acc/mean1": 0.890733987977759, "data_time": 0.6582176468589089, "time": 0.7379298643632368, "step": 98}
{"base_lr": 1.0931863906127332e-05, "lr": 1.0931863906127332e-05, "data_time": 2.2730942368507385, "grad_norm": 1.987059733271599, "loss": 0.014612493861932307, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.014612493861932307, "time": 3.1450525283813477, "epoch": 99, "iter": 2764, "memory": 30731, "step": 2764}
{"base_lr": 1.0931863906127332e-05, "lr": 1.0931863906127332e-05, "data_time": 1.3680765151977539, "grad_norm": 1.9012878715991974, "loss": 0.013801962026627734, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.013801962026627734, "time": 2.1592153668403626, "epoch": 99, "iter": 2772, "memory": 30731, "step": 2772}
{"acc/top1": 0.8880324543610547, "acc/top5": 1.0, "acc/mean1": 0.8888175828230982, "data_time": 0.6256574067202482, "time": 0.7044683803211559, "step": 99}
{"base_lr": 2.733713295369757e-06, "lr": 2.733713295369757e-06, "data_time": 2.257884168624878, "grad_norm": 1.5538780450820924, "loss": 0.006099510396597907, "top1_acc": 1.0, "top5_acc": 1.0, "loss_cls": 0.006099510396597907, "time": 3.171936595439911, "epoch": 100, "iter": 2792, "memory": 30731, "step": 2792}
{"base_lr": 2.733713295369757e-06, "lr": 2.733713295369757e-06, "data_time": 1.346367084980011, "grad_norm": 2.0303883612155915, "loss": 0.00614593816571869, "top1_acc": 0.9814814814814815, "top5_acc": 1.0, "loss_cls": 0.00614593816571869, "time": 2.1448420882225037, "epoch": 100, "iter": 2800, "memory": 30731, "step": 2800}
{"acc/top1": 0.8880324543610547, "acc/top5": 1.0, "acc/mean1": 0.8888524513967884, "data_time": 0.7225588018243964, "time": 0.8021741346879439, "step": 100}
