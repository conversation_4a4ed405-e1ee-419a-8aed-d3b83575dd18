2025/08/08 11:54:48 - mmengine - INFO - 
------------------------------------------------------------
System environment:
    sys.platform: linux
    Python: 3.9.19 (main, May  6 2024, 19:43:03) [GCC 11.2.0]
    CUDA available: True
    MUSA available: False
    numpy_random_seed: 1276333513
    GPU 0: NVIDIA GeForce RTX 4060 Ti
    CUDA_HOME: /usr/local/cuda
    NVCC: Cuda compilation tools, release 11.8, V11.8.89
    GCC: gcc (Ubuntu 11.4.0-1ubuntu1~22.04) 11.4.0
    PyTorch: 2.7.1+cu126
    PyTorch compiling details: PyTorch built with:
  - GCC 11.2
  - C++ Version: 201703
  - Intel(R) oneAPI Math Kernel Library Version 2024.2-Product Build 20240605 for Intel(R) 64 architecture applications
  - Intel(R) MKL-DNN v3.7.1 (Git Hash 8d263e693366ef8db40acc569cc7d8edf644556d)
  - OpenMP 201511 (a.k.a. OpenMP 4.5)
  - LAPACK is enabled (usually provided by MKL)
  - NNPACK is enabled
  - CPU capability usage: AVX2
  - CUDA Runtime 12.6
  - NVCC architecture flags: -gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90
  - CuDNN 90.5.1
  - Magma 2.6.1
  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, COMMIT_SHA=e2d141dbde55c2a4370fac5165b0561b6af4798b, CUDA_VERSION=12.6, CUDNN_VERSION=9.5.1, CXX_COMPILER=/opt/rh/gcc-toolset-11/root/usr/bin/c++, CXX_FLAGS= -D_GLIBCXX_USE_CXX11_ABI=1 -fvisibility-inlines-hidden -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOROCTRACER -DLIBKINETO_NOXPUPTI=ON -DUSE_FBGEMM -DUSE_PYTORCH_QNNPACK -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE -O2 -fPIC -Wall -Wextra -Werror=return-type -Werror=non-virtual-dtor -Werror=range-loop-construct -Werror=bool-operation -Wnarrowing -Wno-missing-field-initializers -Wno-unknown-pragmas -Wno-unused-parameter -Wno-strict-overflow -Wno-strict-aliasing -Wno-stringop-overflow -Wsuggest-override -Wno-psabi -Wno-error=old-style-cast -fdiagnostics-color=always -faligned-new -Wno-maybe-uninitialized -fno-math-errno -fno-trapping-math -Werror=format -Wno-stringop-overflow, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, TORCH_VERSION=2.7.1, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=1, USE_NNPACK=ON, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, 

    TorchVision: 0.22.1+cu126
    OpenCV: 4.12.0
    MMEngine: 0.10.7

Runtime environment:
    cudnn_benchmark: False
    mp_cfg: {'mp_start_method': 'fork', 'opencv_num_threads': 0}
    dist_cfg: {'backend': 'nccl'}
    seed: 1276333513
    diff_rank_seed: False
    deterministic: False
    Distributed launcher: none
    Distributed training: False
    GPU number: 1
------------------------------------------------------------

2025/08/08 11:54:49 - mmengine - INFO - Config:
Test_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl'
TrainVal_ann_file = '/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl'
dataset_type = 'PoseRgbDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=20, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
find_unused_parameters = True
launcher = 'none'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
model = dict(
    cls_head=dict(
        in_channels=512,
        loss_cls=dict(
            class_weight=[
                1.0,
                1.0,
                1.0,
            ],
            loss_weight=1.0,
            type='CrossEntropyLoss'),
        num_classes=3,
        type='TwoFusionHead'),
    fusion_neck=dict(
        dropout=0.5,
        fusion_dim=512,
        fusion_type='attention',
        img_feat_dim=320,
        pose_feat_dim=256,
        type='MultiModalFusionNeck'),
    image_backbone=dict(
        frozen_stages=-1,
        init_cfg=dict(
            checkpoint=
            '../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth',
            type='Pretrained'),
        is_shift=True,
        norm_eval=False,
        num_segments=3,
        out_indices=(6, ),
        pretrained2d=False,
        shift_div=8,
        type='MobileNetV2TSM'),
    pose_backbone=dict(
        graph_cfg=dict(layout='coco', mode='stgcn_spatial'),
        in_channels=3,
        init_cfg=dict(
            checkpoint=
            '../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth',
            type='Pretrained'),
        type='STGCN'),
    test_cfg=dict(average_clips='prob'),
    train_cfg=None,
    type='MultiModalRecognizer')
num_classes = 3
optim_wrapper = dict(
    clip_grad=dict(max_norm=20, norm_type=2),
    optimizer=dict(
        betas=(
            0.9,
            0.999,
        ), lr=0.001, type='AdamW', weight_decay=0.0001),
    paramwise_cfg=dict(
        custom_keys=dict(
            cls_head=dict(lr_mult=1.0),
            fusion_neck=dict(lr_mult=1.0),
            image_backbone=dict(lr_mult=0.1),
            pose_backbone=dict(lr_mult=1.0))),
    type='OptimWrapper')
param_scheduler = [
    dict(begin=0, by_epoch=True, end=5, start_factor=0.01, type='LinearLR'),
    dict(
        T_max=95,
        begin=5,
        by_epoch=True,
        end=100,
        eta_min=1e-06,
        type='CosineAnnealingLR'),
]
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)
resume = False
split_label = dict({
    0: [
        1,
        2,
    ],
    1: [
        0,
    ]
})
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=64,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(input_format='NCHW', type='FormatShape'),
            dict(
                collect_keys=(
                    'keypoint',
                    'imgs',
                ),
                pred_txt=
                '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets',
                split_label=dict({
                    0: [
                        1,
                        2,
                    ],
                    1: [
                        0,
                    ]
                }),
                type='PackActionInputs_Test'),
        ],
        split=None,
        test_mode=True,
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = [
    dict(type='ClassifyReport'),
]
test_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(input_format='NCHW', type='FormatShape'),
    dict(
        collect_keys=(
            'keypoint',
            'imgs',
        ),
        pred_txt=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets',
        split_label=dict({
            0: [
                1,
                2,
            ],
            1: [
                0,
            ]
        }),
        type='PackActionInputs_Test'),
]
train_cfg = dict(
    max_epochs=100, type='EpochBasedTrainLoop', val_begin=1, val_interval=1)
train_dataloader = dict(
    batch_size=128,
    dataset=dict(
        ann_file=
        '/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, seed=255, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                load_Resized_img=True,
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(type='ColorJitter'),
            dict(flip_ratio=0.5, type='Flip'),
            dict(input_format='NCHW', type='FormatShape'),
            dict(collect_keys=(
                'keypoint',
                'imgs',
            ), type='PackActionInputs'),
        ],
        split='train',
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, seed=255, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        load_Resized_img=True,
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(type='ColorJitter'),
    dict(flip_ratio=0.5, type='Flip'),
    dict(input_format='NCHW', type='FormatShape'),
    dict(collect_keys=(
        'keypoint',
        'imgs',
    ), type='PackActionInputs'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=64,
    dataset=dict(
        ann_file=
        '/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                load_Resized_img=True,
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(input_format='NCHW', type='FormatShape'),
            dict(collect_keys=(
                'keypoint',
                'imgs',
            ), type='PackActionInputs'),
        ],
        split='val',
        test_mode=True,
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = [
    dict(type='AccMetric'),
]
val_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        load_Resized_img=True,
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(input_format='NCHW', type='FormatShape'),
    dict(collect_keys=(
        'keypoint',
        'imgs',
    ), type='PackActionInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    type='ActionVisualizer', vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = './work_dirs/pose_rgb_fusion'

2025/08/08 11:54:51 - mmengine - INFO - Distributed training is not used, all SyncBatchNorm (SyncBN) layers in the model will be automatically reverted to BatchNormXd layers if they are used.
2025/08/08 11:54:51 - mmengine - INFO - Hooks will be executed in the following order:
before_run:
(VERY_HIGH   ) RuntimeInfoHook                    
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
before_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_train_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(NORMAL      ) DistSamplerSeedHook                
 -------------------- 
before_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_train_iter:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_train_epoch:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SyncBuffersHook                    
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_val_epoch:
(NORMAL      ) IterTimerHook                      
(NORMAL      ) SyncBuffersHook                    
 -------------------- 
before_val_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_val_iter:
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_val_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
(LOW         ) ParamSchedulerHook                 
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
after_val:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_train:
(VERY_HIGH   ) RuntimeInfoHook                    
(VERY_LOW    ) CheckpointHook                     
 -------------------- 
before_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
before_test_epoch:
(NORMAL      ) IterTimerHook                      
 -------------------- 
before_test_iter:
(NORMAL      ) IterTimerHook                      
 -------------------- 
after_test_iter:
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test_epoch:
(VERY_HIGH   ) RuntimeInfoHook                    
(NORMAL      ) IterTimerHook                      
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
after_test:
(VERY_HIGH   ) RuntimeInfoHook                    
 -------------------- 
after_run:
(BELOW_NORMAL) LoggerHook                         
 -------------------- 
2025/08/08 11:54:51 - mmengine - INFO - 344 videos remain after valid thresholding
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.data_bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.0.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.1.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.2.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.3.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.4.residual.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.5.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.6.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.7.residual.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.8.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.PA:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.PA:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.PA:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.gcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.conv.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- pose_backbone.gcn.9.tcn.bn.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer1.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer2.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer3.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer4.3.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer5.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.1.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.0.net.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer6.2.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.0.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.1.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.layer7.0.conv.2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.conv.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.conv.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.conv.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.weight:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.weight:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.bias:lr=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- image_backbone.conv2.bn.bias:lr_mult=0.1
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.pose_proj.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.img_proj.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.in_proj_bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.multihead_attn.out_proj.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- fusion_neck_model.norm.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.0.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.2.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.4.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_layers.6.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.weight:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.weight:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.weight:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.bias:lr=0.001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.bias:weight_decay=0.0001
2025/08/08 11:54:52 - mmengine - INFO - paramwise_options -- cls_head.fc_cls.bias:lr_mult=1.0
2025/08/08 11:54:52 - mmengine - INFO - 58 videos remain after valid thresholding
2025/08/08 11:54:52 - mmengine - INFO - load model from: ../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth
2025/08/08 11:54:52 - mmengine - INFO - Loads checkpoint by local backend from path: ../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth
2025/08/08 11:54:52 - mmengine - WARNING - The model and loaded state dict do not match exactly

unexpected key in source state_dict: backbone.conv1.conv.weight, backbone.conv1.bn.weight, backbone.conv1.bn.bias, backbone.conv1.bn.running_mean, backbone.conv1.bn.running_var, backbone.conv1.bn.num_batches_tracked, backbone.layer1.0.conv.0.conv.weight, backbone.layer1.0.conv.0.bn.weight, backbone.layer1.0.conv.0.bn.bias, backbone.layer1.0.conv.0.bn.running_mean, backbone.layer1.0.conv.0.bn.running_var, backbone.layer1.0.conv.0.bn.num_batches_tracked, backbone.layer1.0.conv.1.conv.weight, backbone.layer1.0.conv.1.bn.weight, backbone.layer1.0.conv.1.bn.bias, backbone.layer1.0.conv.1.bn.running_mean, backbone.layer1.0.conv.1.bn.running_var, backbone.layer1.0.conv.1.bn.num_batches_tracked, backbone.layer2.0.conv.0.conv.weight, backbone.layer2.0.conv.0.bn.weight, backbone.layer2.0.conv.0.bn.bias, backbone.layer2.0.conv.0.bn.running_mean, backbone.layer2.0.conv.0.bn.running_var, backbone.layer2.0.conv.0.bn.num_batches_tracked, backbone.layer2.0.conv.1.conv.weight, backbone.layer2.0.conv.1.bn.weight, backbone.layer2.0.conv.1.bn.bias, backbone.layer2.0.conv.1.bn.running_mean, backbone.layer2.0.conv.1.bn.running_var, backbone.layer2.0.conv.1.bn.num_batches_tracked, backbone.layer2.0.conv.2.conv.weight, backbone.layer2.0.conv.2.bn.weight, backbone.layer2.0.conv.2.bn.bias, backbone.layer2.0.conv.2.bn.running_mean, backbone.layer2.0.conv.2.bn.running_var, backbone.layer2.0.conv.2.bn.num_batches_tracked, backbone.layer2.1.conv.0.net.conv.weight, backbone.layer2.1.conv.0.net.bn.weight, backbone.layer2.1.conv.0.net.bn.bias, backbone.layer2.1.conv.0.net.bn.running_mean, backbone.layer2.1.conv.0.net.bn.running_var, backbone.layer2.1.conv.0.net.bn.num_batches_tracked, backbone.layer2.1.conv.1.conv.weight, backbone.layer2.1.conv.1.bn.weight, backbone.layer2.1.conv.1.bn.bias, backbone.layer2.1.conv.1.bn.running_mean, backbone.layer2.1.conv.1.bn.running_var, backbone.layer2.1.conv.1.bn.num_batches_tracked, backbone.layer2.1.conv.2.conv.weight, backbone.layer2.1.conv.2.bn.weight, backbone.layer2.1.conv.2.bn.bias, backbone.layer2.1.conv.2.bn.running_mean, backbone.layer2.1.conv.2.bn.running_var, backbone.layer2.1.conv.2.bn.num_batches_tracked, backbone.layer3.0.conv.0.conv.weight, backbone.layer3.0.conv.0.bn.weight, backbone.layer3.0.conv.0.bn.bias, backbone.layer3.0.conv.0.bn.running_mean, backbone.layer3.0.conv.0.bn.running_var, backbone.layer3.0.conv.0.bn.num_batches_tracked, backbone.layer3.0.conv.1.conv.weight, backbone.layer3.0.conv.1.bn.weight, backbone.layer3.0.conv.1.bn.bias, backbone.layer3.0.conv.1.bn.running_mean, backbone.layer3.0.conv.1.bn.running_var, backbone.layer3.0.conv.1.bn.num_batches_tracked, backbone.layer3.0.conv.2.conv.weight, backbone.layer3.0.conv.2.bn.weight, backbone.layer3.0.conv.2.bn.bias, backbone.layer3.0.conv.2.bn.running_mean, backbone.layer3.0.conv.2.bn.running_var, backbone.layer3.0.conv.2.bn.num_batches_tracked, backbone.layer3.1.conv.0.net.conv.weight, backbone.layer3.1.conv.0.net.bn.weight, backbone.layer3.1.conv.0.net.bn.bias, backbone.layer3.1.conv.0.net.bn.running_mean, backbone.layer3.1.conv.0.net.bn.running_var, backbone.layer3.1.conv.0.net.bn.num_batches_tracked, backbone.layer3.1.conv.1.conv.weight, backbone.layer3.1.conv.1.bn.weight, backbone.layer3.1.conv.1.bn.bias, backbone.layer3.1.conv.1.bn.running_mean, backbone.layer3.1.conv.1.bn.running_var, backbone.layer3.1.conv.1.bn.num_batches_tracked, backbone.layer3.1.conv.2.conv.weight, backbone.layer3.1.conv.2.bn.weight, backbone.layer3.1.conv.2.bn.bias, backbone.layer3.1.conv.2.bn.running_mean, backbone.layer3.1.conv.2.bn.running_var, backbone.layer3.1.conv.2.bn.num_batches_tracked, backbone.layer3.2.conv.0.net.conv.weight, backbone.layer3.2.conv.0.net.bn.weight, backbone.layer3.2.conv.0.net.bn.bias, backbone.layer3.2.conv.0.net.bn.running_mean, backbone.layer3.2.conv.0.net.bn.running_var, backbone.layer3.2.conv.0.net.bn.num_batches_tracked, backbone.layer3.2.conv.1.conv.weight, backbone.layer3.2.conv.1.bn.weight, backbone.layer3.2.conv.1.bn.bias, backbone.layer3.2.conv.1.bn.running_mean, backbone.layer3.2.conv.1.bn.running_var, backbone.layer3.2.conv.1.bn.num_batches_tracked, backbone.layer3.2.conv.2.conv.weight, backbone.layer3.2.conv.2.bn.weight, backbone.layer3.2.conv.2.bn.bias, backbone.layer3.2.conv.2.bn.running_mean, backbone.layer3.2.conv.2.bn.running_var, backbone.layer3.2.conv.2.bn.num_batches_tracked, backbone.layer4.0.conv.0.conv.weight, backbone.layer4.0.conv.0.bn.weight, backbone.layer4.0.conv.0.bn.bias, backbone.layer4.0.conv.0.bn.running_mean, backbone.layer4.0.conv.0.bn.running_var, backbone.layer4.0.conv.0.bn.num_batches_tracked, backbone.layer4.0.conv.1.conv.weight, backbone.layer4.0.conv.1.bn.weight, backbone.layer4.0.conv.1.bn.bias, backbone.layer4.0.conv.1.bn.running_mean, backbone.layer4.0.conv.1.bn.running_var, backbone.layer4.0.conv.1.bn.num_batches_tracked, backbone.layer4.0.conv.2.conv.weight, backbone.layer4.0.conv.2.bn.weight, backbone.layer4.0.conv.2.bn.bias, backbone.layer4.0.conv.2.bn.running_mean, backbone.layer4.0.conv.2.bn.running_var, backbone.layer4.0.conv.2.bn.num_batches_tracked, backbone.layer4.1.conv.0.net.conv.weight, backbone.layer4.1.conv.0.net.bn.weight, backbone.layer4.1.conv.0.net.bn.bias, backbone.layer4.1.conv.0.net.bn.running_mean, backbone.layer4.1.conv.0.net.bn.running_var, backbone.layer4.1.conv.0.net.bn.num_batches_tracked, backbone.layer4.1.conv.1.conv.weight, backbone.layer4.1.conv.1.bn.weight, backbone.layer4.1.conv.1.bn.bias, backbone.layer4.1.conv.1.bn.running_mean, backbone.layer4.1.conv.1.bn.running_var, backbone.layer4.1.conv.1.bn.num_batches_tracked, backbone.layer4.1.conv.2.conv.weight, backbone.layer4.1.conv.2.bn.weight, backbone.layer4.1.conv.2.bn.bias, backbone.layer4.1.conv.2.bn.running_mean, backbone.layer4.1.conv.2.bn.running_var, backbone.layer4.1.conv.2.bn.num_batches_tracked, backbone.layer4.2.conv.0.net.conv.weight, backbone.layer4.2.conv.0.net.bn.weight, backbone.layer4.2.conv.0.net.bn.bias, backbone.layer4.2.conv.0.net.bn.running_mean, backbone.layer4.2.conv.0.net.bn.running_var, backbone.layer4.2.conv.0.net.bn.num_batches_tracked, backbone.layer4.2.conv.1.conv.weight, backbone.layer4.2.conv.1.bn.weight, backbone.layer4.2.conv.1.bn.bias, backbone.layer4.2.conv.1.bn.running_mean, backbone.layer4.2.conv.1.bn.running_var, backbone.layer4.2.conv.1.bn.num_batches_tracked, backbone.layer4.2.conv.2.conv.weight, backbone.layer4.2.conv.2.bn.weight, backbone.layer4.2.conv.2.bn.bias, backbone.layer4.2.conv.2.bn.running_mean, backbone.layer4.2.conv.2.bn.running_var, backbone.layer4.2.conv.2.bn.num_batches_tracked, backbone.layer4.3.conv.0.net.conv.weight, backbone.layer4.3.conv.0.net.bn.weight, backbone.layer4.3.conv.0.net.bn.bias, backbone.layer4.3.conv.0.net.bn.running_mean, backbone.layer4.3.conv.0.net.bn.running_var, backbone.layer4.3.conv.0.net.bn.num_batches_tracked, backbone.layer4.3.conv.1.conv.weight, backbone.layer4.3.conv.1.bn.weight, backbone.layer4.3.conv.1.bn.bias, backbone.layer4.3.conv.1.bn.running_mean, backbone.layer4.3.conv.1.bn.running_var, backbone.layer4.3.conv.1.bn.num_batches_tracked, backbone.layer4.3.conv.2.conv.weight, backbone.layer4.3.conv.2.bn.weight, backbone.layer4.3.conv.2.bn.bias, backbone.layer4.3.conv.2.bn.running_mean, backbone.layer4.3.conv.2.bn.running_var, backbone.layer4.3.conv.2.bn.num_batches_tracked, backbone.layer5.0.conv.0.conv.weight, backbone.layer5.0.conv.0.bn.weight, backbone.layer5.0.conv.0.bn.bias, backbone.layer5.0.conv.0.bn.running_mean, backbone.layer5.0.conv.0.bn.running_var, backbone.layer5.0.conv.0.bn.num_batches_tracked, backbone.layer5.0.conv.1.conv.weight, backbone.layer5.0.conv.1.bn.weight, backbone.layer5.0.conv.1.bn.bias, backbone.layer5.0.conv.1.bn.running_mean, backbone.layer5.0.conv.1.bn.running_var, backbone.layer5.0.conv.1.bn.num_batches_tracked, backbone.layer5.0.conv.2.conv.weight, backbone.layer5.0.conv.2.bn.weight, backbone.layer5.0.conv.2.bn.bias, backbone.layer5.0.conv.2.bn.running_mean, backbone.layer5.0.conv.2.bn.running_var, backbone.layer5.0.conv.2.bn.num_batches_tracked, backbone.layer5.1.conv.0.net.conv.weight, backbone.layer5.1.conv.0.net.bn.weight, backbone.layer5.1.conv.0.net.bn.bias, backbone.layer5.1.conv.0.net.bn.running_mean, backbone.layer5.1.conv.0.net.bn.running_var, backbone.layer5.1.conv.0.net.bn.num_batches_tracked, backbone.layer5.1.conv.1.conv.weight, backbone.layer5.1.conv.1.bn.weight, backbone.layer5.1.conv.1.bn.bias, backbone.layer5.1.conv.1.bn.running_mean, backbone.layer5.1.conv.1.bn.running_var, backbone.layer5.1.conv.1.bn.num_batches_tracked, backbone.layer5.1.conv.2.conv.weight, backbone.layer5.1.conv.2.bn.weight, backbone.layer5.1.conv.2.bn.bias, backbone.layer5.1.conv.2.bn.running_mean, backbone.layer5.1.conv.2.bn.running_var, backbone.layer5.1.conv.2.bn.num_batches_tracked, backbone.layer5.2.conv.0.net.conv.weight, backbone.layer5.2.conv.0.net.bn.weight, backbone.layer5.2.conv.0.net.bn.bias, backbone.layer5.2.conv.0.net.bn.running_mean, backbone.layer5.2.conv.0.net.bn.running_var, backbone.layer5.2.conv.0.net.bn.num_batches_tracked, backbone.layer5.2.conv.1.conv.weight, backbone.layer5.2.conv.1.bn.weight, backbone.layer5.2.conv.1.bn.bias, backbone.layer5.2.conv.1.bn.running_mean, backbone.layer5.2.conv.1.bn.running_var, backbone.layer5.2.conv.1.bn.num_batches_tracked, backbone.layer5.2.conv.2.conv.weight, backbone.layer5.2.conv.2.bn.weight, backbone.layer5.2.conv.2.bn.bias, backbone.layer5.2.conv.2.bn.running_mean, backbone.layer5.2.conv.2.bn.running_var, backbone.layer5.2.conv.2.bn.num_batches_tracked, backbone.layer6.0.conv.0.conv.weight, backbone.layer6.0.conv.0.bn.weight, backbone.layer6.0.conv.0.bn.bias, backbone.layer6.0.conv.0.bn.running_mean, backbone.layer6.0.conv.0.bn.running_var, backbone.layer6.0.conv.0.bn.num_batches_tracked, backbone.layer6.0.conv.1.conv.weight, backbone.layer6.0.conv.1.bn.weight, backbone.layer6.0.conv.1.bn.bias, backbone.layer6.0.conv.1.bn.running_mean, backbone.layer6.0.conv.1.bn.running_var, backbone.layer6.0.conv.1.bn.num_batches_tracked, backbone.layer6.0.conv.2.conv.weight, backbone.layer6.0.conv.2.bn.weight, backbone.layer6.0.conv.2.bn.bias, backbone.layer6.0.conv.2.bn.running_mean, backbone.layer6.0.conv.2.bn.running_var, backbone.layer6.0.conv.2.bn.num_batches_tracked, backbone.layer6.1.conv.0.net.conv.weight, backbone.layer6.1.conv.0.net.bn.weight, backbone.layer6.1.conv.0.net.bn.bias, backbone.layer6.1.conv.0.net.bn.running_mean, backbone.layer6.1.conv.0.net.bn.running_var, backbone.layer6.1.conv.0.net.bn.num_batches_tracked, backbone.layer6.1.conv.1.conv.weight, backbone.layer6.1.conv.1.bn.weight, backbone.layer6.1.conv.1.bn.bias, backbone.layer6.1.conv.1.bn.running_mean, backbone.layer6.1.conv.1.bn.running_var, backbone.layer6.1.conv.1.bn.num_batches_tracked, backbone.layer6.1.conv.2.conv.weight, backbone.layer6.1.conv.2.bn.weight, backbone.layer6.1.conv.2.bn.bias, backbone.layer6.1.conv.2.bn.running_mean, backbone.layer6.1.conv.2.bn.running_var, backbone.layer6.1.conv.2.bn.num_batches_tracked, backbone.layer6.2.conv.0.net.conv.weight, backbone.layer6.2.conv.0.net.bn.weight, backbone.layer6.2.conv.0.net.bn.bias, backbone.layer6.2.conv.0.net.bn.running_mean, backbone.layer6.2.conv.0.net.bn.running_var, backbone.layer6.2.conv.0.net.bn.num_batches_tracked, backbone.layer6.2.conv.1.conv.weight, backbone.layer6.2.conv.1.bn.weight, backbone.layer6.2.conv.1.bn.bias, backbone.layer6.2.conv.1.bn.running_mean, backbone.layer6.2.conv.1.bn.running_var, backbone.layer6.2.conv.1.bn.num_batches_tracked, backbone.layer6.2.conv.2.conv.weight, backbone.layer6.2.conv.2.bn.weight, backbone.layer6.2.conv.2.bn.bias, backbone.layer6.2.conv.2.bn.running_mean, backbone.layer6.2.conv.2.bn.running_var, backbone.layer6.2.conv.2.bn.num_batches_tracked, backbone.layer7.0.conv.0.conv.weight, backbone.layer7.0.conv.0.bn.weight, backbone.layer7.0.conv.0.bn.bias, backbone.layer7.0.conv.0.bn.running_mean, backbone.layer7.0.conv.0.bn.running_var, backbone.layer7.0.conv.0.bn.num_batches_tracked, backbone.layer7.0.conv.1.conv.weight, backbone.layer7.0.conv.1.bn.weight, backbone.layer7.0.conv.1.bn.bias, backbone.layer7.0.conv.1.bn.running_mean, backbone.layer7.0.conv.1.bn.running_var, backbone.layer7.0.conv.1.bn.num_batches_tracked, backbone.layer7.0.conv.2.conv.weight, backbone.layer7.0.conv.2.bn.weight, backbone.layer7.0.conv.2.bn.bias, backbone.layer7.0.conv.2.bn.running_mean, backbone.layer7.0.conv.2.bn.running_var, backbone.layer7.0.conv.2.bn.num_batches_tracked, backbone.conv2.conv.weight, backbone.conv2.bn.weight, backbone.conv2.bn.bias, backbone.conv2.bn.running_mean, backbone.conv2.bn.running_var, backbone.conv2.bn.num_batches_tracked, cls_head.fc_cls.weight, cls_head.fc_cls.bias

missing keys in source state_dict: conv1.conv.weight, conv1.bn.weight, conv1.bn.bias, conv1.bn.running_mean, conv1.bn.running_var, layer1.0.conv.0.conv.weight, layer1.0.conv.0.bn.weight, layer1.0.conv.0.bn.bias, layer1.0.conv.0.bn.running_mean, layer1.0.conv.0.bn.running_var, layer1.0.conv.1.conv.weight, layer1.0.conv.1.bn.weight, layer1.0.conv.1.bn.bias, layer1.0.conv.1.bn.running_mean, layer1.0.conv.1.bn.running_var, layer2.0.conv.0.conv.weight, layer2.0.conv.0.bn.weight, layer2.0.conv.0.bn.bias, layer2.0.conv.0.bn.running_mean, layer2.0.conv.0.bn.running_var, layer2.0.conv.1.conv.weight, layer2.0.conv.1.bn.weight, layer2.0.conv.1.bn.bias, layer2.0.conv.1.bn.running_mean, layer2.0.conv.1.bn.running_var, layer2.0.conv.2.conv.weight, layer2.0.conv.2.bn.weight, layer2.0.conv.2.bn.bias, layer2.0.conv.2.bn.running_mean, layer2.0.conv.2.bn.running_var, layer2.1.conv.0.net.conv.weight, layer2.1.conv.0.net.bn.weight, layer2.1.conv.0.net.bn.bias, layer2.1.conv.0.net.bn.running_mean, layer2.1.conv.0.net.bn.running_var, layer2.1.conv.1.conv.weight, layer2.1.conv.1.bn.weight, layer2.1.conv.1.bn.bias, layer2.1.conv.1.bn.running_mean, layer2.1.conv.1.bn.running_var, layer2.1.conv.2.conv.weight, layer2.1.conv.2.bn.weight, layer2.1.conv.2.bn.bias, layer2.1.conv.2.bn.running_mean, layer2.1.conv.2.bn.running_var, layer3.0.conv.0.conv.weight, layer3.0.conv.0.bn.weight, layer3.0.conv.0.bn.bias, layer3.0.conv.0.bn.running_mean, layer3.0.conv.0.bn.running_var, layer3.0.conv.1.conv.weight, layer3.0.conv.1.bn.weight, layer3.0.conv.1.bn.bias, layer3.0.conv.1.bn.running_mean, layer3.0.conv.1.bn.running_var, layer3.0.conv.2.conv.weight, layer3.0.conv.2.bn.weight, layer3.0.conv.2.bn.bias, layer3.0.conv.2.bn.running_mean, layer3.0.conv.2.bn.running_var, layer3.1.conv.0.net.conv.weight, layer3.1.conv.0.net.bn.weight, layer3.1.conv.0.net.bn.bias, layer3.1.conv.0.net.bn.running_mean, layer3.1.conv.0.net.bn.running_var, layer3.1.conv.1.conv.weight, layer3.1.conv.1.bn.weight, layer3.1.conv.1.bn.bias, layer3.1.conv.1.bn.running_mean, layer3.1.conv.1.bn.running_var, layer3.1.conv.2.conv.weight, layer3.1.conv.2.bn.weight, layer3.1.conv.2.bn.bias, layer3.1.conv.2.bn.running_mean, layer3.1.conv.2.bn.running_var, layer3.2.conv.0.net.conv.weight, layer3.2.conv.0.net.bn.weight, layer3.2.conv.0.net.bn.bias, layer3.2.conv.0.net.bn.running_mean, layer3.2.conv.0.net.bn.running_var, layer3.2.conv.1.conv.weight, layer3.2.conv.1.bn.weight, layer3.2.conv.1.bn.bias, layer3.2.conv.1.bn.running_mean, layer3.2.conv.1.bn.running_var, layer3.2.conv.2.conv.weight, layer3.2.conv.2.bn.weight, layer3.2.conv.2.bn.bias, layer3.2.conv.2.bn.running_mean, layer3.2.conv.2.bn.running_var, layer4.0.conv.0.conv.weight, layer4.0.conv.0.bn.weight, layer4.0.conv.0.bn.bias, layer4.0.conv.0.bn.running_mean, layer4.0.conv.0.bn.running_var, layer4.0.conv.1.conv.weight, layer4.0.conv.1.bn.weight, layer4.0.conv.1.bn.bias, layer4.0.conv.1.bn.running_mean, layer4.0.conv.1.bn.running_var, layer4.0.conv.2.conv.weight, layer4.0.conv.2.bn.weight, layer4.0.conv.2.bn.bias, layer4.0.conv.2.bn.running_mean, layer4.0.conv.2.bn.running_var, layer4.1.conv.0.net.conv.weight, layer4.1.conv.0.net.bn.weight, layer4.1.conv.0.net.bn.bias, layer4.1.conv.0.net.bn.running_mean, layer4.1.conv.0.net.bn.running_var, layer4.1.conv.1.conv.weight, layer4.1.conv.1.bn.weight, layer4.1.conv.1.bn.bias, layer4.1.conv.1.bn.running_mean, layer4.1.conv.1.bn.running_var, layer4.1.conv.2.conv.weight, layer4.1.conv.2.bn.weight, layer4.1.conv.2.bn.bias, layer4.1.conv.2.bn.running_mean, layer4.1.conv.2.bn.running_var, layer4.2.conv.0.net.conv.weight, layer4.2.conv.0.net.bn.weight, layer4.2.conv.0.net.bn.bias, layer4.2.conv.0.net.bn.running_mean, layer4.2.conv.0.net.bn.running_var, layer4.2.conv.1.conv.weight, layer4.2.conv.1.bn.weight, layer4.2.conv.1.bn.bias, layer4.2.conv.1.bn.running_mean, layer4.2.conv.1.bn.running_var, layer4.2.conv.2.conv.weight, layer4.2.conv.2.bn.weight, layer4.2.conv.2.bn.bias, layer4.2.conv.2.bn.running_mean, layer4.2.conv.2.bn.running_var, layer4.3.conv.0.net.conv.weight, layer4.3.conv.0.net.bn.weight, layer4.3.conv.0.net.bn.bias, layer4.3.conv.0.net.bn.running_mean, layer4.3.conv.0.net.bn.running_var, layer4.3.conv.1.conv.weight, layer4.3.conv.1.bn.weight, layer4.3.conv.1.bn.bias, layer4.3.conv.1.bn.running_mean, layer4.3.conv.1.bn.running_var, layer4.3.conv.2.conv.weight, layer4.3.conv.2.bn.weight, layer4.3.conv.2.bn.bias, layer4.3.conv.2.bn.running_mean, layer4.3.conv.2.bn.running_var, layer5.0.conv.0.conv.weight, layer5.0.conv.0.bn.weight, layer5.0.conv.0.bn.bias, layer5.0.conv.0.bn.running_mean, layer5.0.conv.0.bn.running_var, layer5.0.conv.1.conv.weight, layer5.0.conv.1.bn.weight, layer5.0.conv.1.bn.bias, layer5.0.conv.1.bn.running_mean, layer5.0.conv.1.bn.running_var, layer5.0.conv.2.conv.weight, layer5.0.conv.2.bn.weight, layer5.0.conv.2.bn.bias, layer5.0.conv.2.bn.running_mean, layer5.0.conv.2.bn.running_var, layer5.1.conv.0.net.conv.weight, layer5.1.conv.0.net.bn.weight, layer5.1.conv.0.net.bn.bias, layer5.1.conv.0.net.bn.running_mean, layer5.1.conv.0.net.bn.running_var, layer5.1.conv.1.conv.weight, layer5.1.conv.1.bn.weight, layer5.1.conv.1.bn.bias, layer5.1.conv.1.bn.running_mean, layer5.1.conv.1.bn.running_var, layer5.1.conv.2.conv.weight, layer5.1.conv.2.bn.weight, layer5.1.conv.2.bn.bias, layer5.1.conv.2.bn.running_mean, layer5.1.conv.2.bn.running_var, layer5.2.conv.0.net.conv.weight, layer5.2.conv.0.net.bn.weight, layer5.2.conv.0.net.bn.bias, layer5.2.conv.0.net.bn.running_mean, layer5.2.conv.0.net.bn.running_var, layer5.2.conv.1.conv.weight, layer5.2.conv.1.bn.weight, layer5.2.conv.1.bn.bias, layer5.2.conv.1.bn.running_mean, layer5.2.conv.1.bn.running_var, layer5.2.conv.2.conv.weight, layer5.2.conv.2.bn.weight, layer5.2.conv.2.bn.bias, layer5.2.conv.2.bn.running_mean, layer5.2.conv.2.bn.running_var, layer6.0.conv.0.conv.weight, layer6.0.conv.0.bn.weight, layer6.0.conv.0.bn.bias, layer6.0.conv.0.bn.running_mean, layer6.0.conv.0.bn.running_var, layer6.0.conv.1.conv.weight, layer6.0.conv.1.bn.weight, layer6.0.conv.1.bn.bias, layer6.0.conv.1.bn.running_mean, layer6.0.conv.1.bn.running_var, layer6.0.conv.2.conv.weight, layer6.0.conv.2.bn.weight, layer6.0.conv.2.bn.bias, layer6.0.conv.2.bn.running_mean, layer6.0.conv.2.bn.running_var, layer6.1.conv.0.net.conv.weight, layer6.1.conv.0.net.bn.weight, layer6.1.conv.0.net.bn.bias, layer6.1.conv.0.net.bn.running_mean, layer6.1.conv.0.net.bn.running_var, layer6.1.conv.1.conv.weight, layer6.1.conv.1.bn.weight, layer6.1.conv.1.bn.bias, layer6.1.conv.1.bn.running_mean, layer6.1.conv.1.bn.running_var, layer6.1.conv.2.conv.weight, layer6.1.conv.2.bn.weight, layer6.1.conv.2.bn.bias, layer6.1.conv.2.bn.running_mean, layer6.1.conv.2.bn.running_var, layer6.2.conv.0.net.conv.weight, layer6.2.conv.0.net.bn.weight, layer6.2.conv.0.net.bn.bias, layer6.2.conv.0.net.bn.running_mean, layer6.2.conv.0.net.bn.running_var, layer6.2.conv.1.conv.weight, layer6.2.conv.1.bn.weight, layer6.2.conv.1.bn.bias, layer6.2.conv.1.bn.running_mean, layer6.2.conv.1.bn.running_var, layer6.2.conv.2.conv.weight, layer6.2.conv.2.bn.weight, layer6.2.conv.2.bn.bias, layer6.2.conv.2.bn.running_mean, layer6.2.conv.2.bn.running_var, layer7.0.conv.0.conv.weight, layer7.0.conv.0.bn.weight, layer7.0.conv.0.bn.bias, layer7.0.conv.0.bn.running_mean, layer7.0.conv.0.bn.running_var, layer7.0.conv.1.conv.weight, layer7.0.conv.1.bn.weight, layer7.0.conv.1.bn.bias, layer7.0.conv.1.bn.running_mean, layer7.0.conv.1.bn.running_var, layer7.0.conv.2.conv.weight, layer7.0.conv.2.bn.weight, layer7.0.conv.2.bn.bias, layer7.0.conv.2.bn.running_mean, layer7.0.conv.2.bn.running_var, conv2.conv.weight, conv2.bn.weight, conv2.bn.bias, conv2.bn.running_mean, conv2.bn.running_var

Name of parameter - Initialization information

conv1.conv.weight - torch.Size([32, 3, 3, 3]): 
Initialized by user-defined `init_weights` in ConvModule  

conv1.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

conv1.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.0.conv.weight - torch.Size([32, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.0.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.0.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.1.conv.weight - torch.Size([16, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.1.bn.weight - torch.Size([16]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer1.0.conv.1.bn.bias - torch.Size([16]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.0.conv.weight - torch.Size([96, 16, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.0.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.0.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.1.conv.weight - torch.Size([96, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.1.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.1.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.2.conv.weight - torch.Size([24, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.2.bn.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.0.conv.2.bn.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.0.net.conv.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.0.net.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.0.net.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.1.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.1.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.2.conv.weight - torch.Size([24, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.2.bn.weight - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer2.1.conv.2.bn.bias - torch.Size([24]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.0.conv.weight - torch.Size([144, 24, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.0.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.0.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.1.conv.weight - torch.Size([144, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.1.bn.weight - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.1.bn.bias - torch.Size([144]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.2.conv.weight - torch.Size([32, 144, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.2.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.0.conv.2.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.0.net.conv.weight - torch.Size([192, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.0.net.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.0.net.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.1.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.1.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.2.conv.weight - torch.Size([32, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.2.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.1.conv.2.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.0.net.conv.weight - torch.Size([192, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.0.net.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.0.net.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.1.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.1.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.2.conv.weight - torch.Size([32, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.2.bn.weight - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer3.2.conv.2.bn.bias - torch.Size([32]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.0.conv.weight - torch.Size([192, 32, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.0.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.0.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.1.conv.weight - torch.Size([192, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.1.bn.weight - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.1.bn.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.2.conv.weight - torch.Size([64, 192, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.0.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.0.net.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.0.net.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.0.net.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.2.conv.weight - torch.Size([64, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.1.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.0.net.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.0.net.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.0.net.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.2.conv.weight - torch.Size([64, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.2.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.0.net.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.0.net.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.0.net.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.2.conv.weight - torch.Size([64, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.2.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer4.3.conv.2.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.0.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.0.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.0.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.1.conv.weight - torch.Size([384, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.1.bn.weight - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.1.bn.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.2.conv.weight - torch.Size([96, 384, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.2.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.0.conv.2.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.0.net.conv.weight - torch.Size([576, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.0.net.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.0.net.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.1.conv.weight - torch.Size([576, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.1.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.1.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.2.conv.weight - torch.Size([96, 576, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.2.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.1.conv.2.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.0.net.conv.weight - torch.Size([576, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.0.net.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.0.net.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.1.conv.weight - torch.Size([576, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.1.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.1.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.2.conv.weight - torch.Size([96, 576, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.2.bn.weight - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer5.2.conv.2.bn.bias - torch.Size([96]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.0.conv.weight - torch.Size([576, 96, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.0.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.0.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.1.conv.weight - torch.Size([576, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.1.bn.weight - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.1.bn.bias - torch.Size([576]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.2.conv.weight - torch.Size([160, 576, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.2.bn.weight - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.0.conv.2.bn.bias - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.0.net.conv.weight - torch.Size([960, 160, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.0.net.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.0.net.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.1.conv.weight - torch.Size([960, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.1.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.1.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.2.conv.weight - torch.Size([160, 960, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.2.bn.weight - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.1.conv.2.bn.bias - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.0.net.conv.weight - torch.Size([960, 160, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.0.net.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.0.net.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.1.conv.weight - torch.Size([960, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.1.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.1.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.2.conv.weight - torch.Size([160, 960, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.2.bn.weight - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer6.2.conv.2.bn.bias - torch.Size([160]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.0.conv.weight - torch.Size([960, 160, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.0.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.0.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.1.conv.weight - torch.Size([960, 1, 3, 3]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.1.bn.weight - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.1.bn.bias - torch.Size([960]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.2.conv.weight - torch.Size([320, 960, 1, 1]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.2.bn.weight - torch.Size([320]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

layer7.0.conv.2.bn.bias - torch.Size([320]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

conv2.conv.weight - torch.Size([1280, 320, 1, 1]): 
Initialized by user-defined `init_weights` in ConvModule  

conv2.bn.weight - torch.Size([1280]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  

conv2.bn.bias - torch.Size([1280]): 
The value is the same before and after calling `init_weights` of MobileNetV2TSM  
2025/08/08 11:54:52 - mmengine - INFO - load model from: ../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth
2025/08/08 11:54:52 - mmengine - INFO - Loads checkpoint by local backend from path: ../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth
2025/08/08 11:54:52 - mmengine - WARNING - The model and loaded state dict do not match exactly

unexpected key in source state_dict: backbone.data_bn.weight, backbone.data_bn.bias, backbone.data_bn.running_mean, backbone.data_bn.running_var, backbone.data_bn.num_batches_tracked, backbone.gcn.0.gcn.PA, backbone.gcn.0.gcn.A, backbone.gcn.0.gcn.bn.weight, backbone.gcn.0.gcn.bn.bias, backbone.gcn.0.gcn.bn.running_mean, backbone.gcn.0.gcn.bn.running_var, backbone.gcn.0.gcn.bn.num_batches_tracked, backbone.gcn.0.gcn.conv.weight, backbone.gcn.0.gcn.conv.bias, backbone.gcn.0.tcn.conv.weight, backbone.gcn.0.tcn.conv.bias, backbone.gcn.0.tcn.bn.weight, backbone.gcn.0.tcn.bn.bias, backbone.gcn.0.tcn.bn.running_mean, backbone.gcn.0.tcn.bn.running_var, backbone.gcn.0.tcn.bn.num_batches_tracked, backbone.gcn.1.gcn.PA, backbone.gcn.1.gcn.A, backbone.gcn.1.gcn.bn.weight, backbone.gcn.1.gcn.bn.bias, backbone.gcn.1.gcn.bn.running_mean, backbone.gcn.1.gcn.bn.running_var, backbone.gcn.1.gcn.bn.num_batches_tracked, backbone.gcn.1.gcn.conv.weight, backbone.gcn.1.gcn.conv.bias, backbone.gcn.1.tcn.conv.weight, backbone.gcn.1.tcn.conv.bias, backbone.gcn.1.tcn.bn.weight, backbone.gcn.1.tcn.bn.bias, backbone.gcn.1.tcn.bn.running_mean, backbone.gcn.1.tcn.bn.running_var, backbone.gcn.1.tcn.bn.num_batches_tracked, backbone.gcn.2.gcn.PA, backbone.gcn.2.gcn.A, backbone.gcn.2.gcn.bn.weight, backbone.gcn.2.gcn.bn.bias, backbone.gcn.2.gcn.bn.running_mean, backbone.gcn.2.gcn.bn.running_var, backbone.gcn.2.gcn.bn.num_batches_tracked, backbone.gcn.2.gcn.conv.weight, backbone.gcn.2.gcn.conv.bias, backbone.gcn.2.tcn.conv.weight, backbone.gcn.2.tcn.conv.bias, backbone.gcn.2.tcn.bn.weight, backbone.gcn.2.tcn.bn.bias, backbone.gcn.2.tcn.bn.running_mean, backbone.gcn.2.tcn.bn.running_var, backbone.gcn.2.tcn.bn.num_batches_tracked, backbone.gcn.3.gcn.PA, backbone.gcn.3.gcn.A, backbone.gcn.3.gcn.bn.weight, backbone.gcn.3.gcn.bn.bias, backbone.gcn.3.gcn.bn.running_mean, backbone.gcn.3.gcn.bn.running_var, backbone.gcn.3.gcn.bn.num_batches_tracked, backbone.gcn.3.gcn.conv.weight, backbone.gcn.3.gcn.conv.bias, backbone.gcn.3.tcn.conv.weight, backbone.gcn.3.tcn.conv.bias, backbone.gcn.3.tcn.bn.weight, backbone.gcn.3.tcn.bn.bias, backbone.gcn.3.tcn.bn.running_mean, backbone.gcn.3.tcn.bn.running_var, backbone.gcn.3.tcn.bn.num_batches_tracked, backbone.gcn.4.gcn.PA, backbone.gcn.4.gcn.A, backbone.gcn.4.gcn.bn.weight, backbone.gcn.4.gcn.bn.bias, backbone.gcn.4.gcn.bn.running_mean, backbone.gcn.4.gcn.bn.running_var, backbone.gcn.4.gcn.bn.num_batches_tracked, backbone.gcn.4.gcn.conv.weight, backbone.gcn.4.gcn.conv.bias, backbone.gcn.4.tcn.conv.weight, backbone.gcn.4.tcn.conv.bias, backbone.gcn.4.tcn.bn.weight, backbone.gcn.4.tcn.bn.bias, backbone.gcn.4.tcn.bn.running_mean, backbone.gcn.4.tcn.bn.running_var, backbone.gcn.4.tcn.bn.num_batches_tracked, backbone.gcn.4.residual.conv.weight, backbone.gcn.4.residual.conv.bias, backbone.gcn.4.residual.bn.weight, backbone.gcn.4.residual.bn.bias, backbone.gcn.4.residual.bn.running_mean, backbone.gcn.4.residual.bn.running_var, backbone.gcn.4.residual.bn.num_batches_tracked, backbone.gcn.5.gcn.PA, backbone.gcn.5.gcn.A, backbone.gcn.5.gcn.bn.weight, backbone.gcn.5.gcn.bn.bias, backbone.gcn.5.gcn.bn.running_mean, backbone.gcn.5.gcn.bn.running_var, backbone.gcn.5.gcn.bn.num_batches_tracked, backbone.gcn.5.gcn.conv.weight, backbone.gcn.5.gcn.conv.bias, backbone.gcn.5.tcn.conv.weight, backbone.gcn.5.tcn.conv.bias, backbone.gcn.5.tcn.bn.weight, backbone.gcn.5.tcn.bn.bias, backbone.gcn.5.tcn.bn.running_mean, backbone.gcn.5.tcn.bn.running_var, backbone.gcn.5.tcn.bn.num_batches_tracked, backbone.gcn.6.gcn.PA, backbone.gcn.6.gcn.A, backbone.gcn.6.gcn.bn.weight, backbone.gcn.6.gcn.bn.bias, backbone.gcn.6.gcn.bn.running_mean, backbone.gcn.6.gcn.bn.running_var, backbone.gcn.6.gcn.bn.num_batches_tracked, backbone.gcn.6.gcn.conv.weight, backbone.gcn.6.gcn.conv.bias, backbone.gcn.6.tcn.conv.weight, backbone.gcn.6.tcn.conv.bias, backbone.gcn.6.tcn.bn.weight, backbone.gcn.6.tcn.bn.bias, backbone.gcn.6.tcn.bn.running_mean, backbone.gcn.6.tcn.bn.running_var, backbone.gcn.6.tcn.bn.num_batches_tracked, backbone.gcn.7.gcn.PA, backbone.gcn.7.gcn.A, backbone.gcn.7.gcn.bn.weight, backbone.gcn.7.gcn.bn.bias, backbone.gcn.7.gcn.bn.running_mean, backbone.gcn.7.gcn.bn.running_var, backbone.gcn.7.gcn.bn.num_batches_tracked, backbone.gcn.7.gcn.conv.weight, backbone.gcn.7.gcn.conv.bias, backbone.gcn.7.tcn.conv.weight, backbone.gcn.7.tcn.conv.bias, backbone.gcn.7.tcn.bn.weight, backbone.gcn.7.tcn.bn.bias, backbone.gcn.7.tcn.bn.running_mean, backbone.gcn.7.tcn.bn.running_var, backbone.gcn.7.tcn.bn.num_batches_tracked, backbone.gcn.7.residual.conv.weight, backbone.gcn.7.residual.conv.bias, backbone.gcn.7.residual.bn.weight, backbone.gcn.7.residual.bn.bias, backbone.gcn.7.residual.bn.running_mean, backbone.gcn.7.residual.bn.running_var, backbone.gcn.7.residual.bn.num_batches_tracked, backbone.gcn.8.gcn.PA, backbone.gcn.8.gcn.A, backbone.gcn.8.gcn.bn.weight, backbone.gcn.8.gcn.bn.bias, backbone.gcn.8.gcn.bn.running_mean, backbone.gcn.8.gcn.bn.running_var, backbone.gcn.8.gcn.bn.num_batches_tracked, backbone.gcn.8.gcn.conv.weight, backbone.gcn.8.gcn.conv.bias, backbone.gcn.8.tcn.conv.weight, backbone.gcn.8.tcn.conv.bias, backbone.gcn.8.tcn.bn.weight, backbone.gcn.8.tcn.bn.bias, backbone.gcn.8.tcn.bn.running_mean, backbone.gcn.8.tcn.bn.running_var, backbone.gcn.8.tcn.bn.num_batches_tracked, backbone.gcn.9.gcn.PA, backbone.gcn.9.gcn.A, backbone.gcn.9.gcn.bn.weight, backbone.gcn.9.gcn.bn.bias, backbone.gcn.9.gcn.bn.running_mean, backbone.gcn.9.gcn.bn.running_var, backbone.gcn.9.gcn.bn.num_batches_tracked, backbone.gcn.9.gcn.conv.weight, backbone.gcn.9.gcn.conv.bias, backbone.gcn.9.tcn.conv.weight, backbone.gcn.9.tcn.conv.bias, backbone.gcn.9.tcn.bn.weight, backbone.gcn.9.tcn.bn.bias, backbone.gcn.9.tcn.bn.running_mean, backbone.gcn.9.tcn.bn.running_var, backbone.gcn.9.tcn.bn.num_batches_tracked, cls_head.fc.weight, cls_head.fc.bias

missing keys in source state_dict: data_bn.weight, data_bn.bias, data_bn.running_mean, data_bn.running_var, gcn.0.gcn.PA, gcn.0.gcn.A, gcn.0.gcn.bn.weight, gcn.0.gcn.bn.bias, gcn.0.gcn.bn.running_mean, gcn.0.gcn.bn.running_var, gcn.0.gcn.conv.weight, gcn.0.gcn.conv.bias, gcn.0.tcn.conv.weight, gcn.0.tcn.conv.bias, gcn.0.tcn.bn.weight, gcn.0.tcn.bn.bias, gcn.0.tcn.bn.running_mean, gcn.0.tcn.bn.running_var, gcn.1.gcn.PA, gcn.1.gcn.A, gcn.1.gcn.bn.weight, gcn.1.gcn.bn.bias, gcn.1.gcn.bn.running_mean, gcn.1.gcn.bn.running_var, gcn.1.gcn.conv.weight, gcn.1.gcn.conv.bias, gcn.1.tcn.conv.weight, gcn.1.tcn.conv.bias, gcn.1.tcn.bn.weight, gcn.1.tcn.bn.bias, gcn.1.tcn.bn.running_mean, gcn.1.tcn.bn.running_var, gcn.2.gcn.PA, gcn.2.gcn.A, gcn.2.gcn.bn.weight, gcn.2.gcn.bn.bias, gcn.2.gcn.bn.running_mean, gcn.2.gcn.bn.running_var, gcn.2.gcn.conv.weight, gcn.2.gcn.conv.bias, gcn.2.tcn.conv.weight, gcn.2.tcn.conv.bias, gcn.2.tcn.bn.weight, gcn.2.tcn.bn.bias, gcn.2.tcn.bn.running_mean, gcn.2.tcn.bn.running_var, gcn.3.gcn.PA, gcn.3.gcn.A, gcn.3.gcn.bn.weight, gcn.3.gcn.bn.bias, gcn.3.gcn.bn.running_mean, gcn.3.gcn.bn.running_var, gcn.3.gcn.conv.weight, gcn.3.gcn.conv.bias, gcn.3.tcn.conv.weight, gcn.3.tcn.conv.bias, gcn.3.tcn.bn.weight, gcn.3.tcn.bn.bias, gcn.3.tcn.bn.running_mean, gcn.3.tcn.bn.running_var, gcn.4.gcn.PA, gcn.4.gcn.A, gcn.4.gcn.bn.weight, gcn.4.gcn.bn.bias, gcn.4.gcn.bn.running_mean, gcn.4.gcn.bn.running_var, gcn.4.gcn.conv.weight, gcn.4.gcn.conv.bias, gcn.4.tcn.conv.weight, gcn.4.tcn.conv.bias, gcn.4.tcn.bn.weight, gcn.4.tcn.bn.bias, gcn.4.tcn.bn.running_mean, gcn.4.tcn.bn.running_var, gcn.4.residual.conv.weight, gcn.4.residual.conv.bias, gcn.4.residual.bn.weight, gcn.4.residual.bn.bias, gcn.4.residual.bn.running_mean, gcn.4.residual.bn.running_var, gcn.5.gcn.PA, gcn.5.gcn.A, gcn.5.gcn.bn.weight, gcn.5.gcn.bn.bias, gcn.5.gcn.bn.running_mean, gcn.5.gcn.bn.running_var, gcn.5.gcn.conv.weight, gcn.5.gcn.conv.bias, gcn.5.tcn.conv.weight, gcn.5.tcn.conv.bias, gcn.5.tcn.bn.weight, gcn.5.tcn.bn.bias, gcn.5.tcn.bn.running_mean, gcn.5.tcn.bn.running_var, gcn.6.gcn.PA, gcn.6.gcn.A, gcn.6.gcn.bn.weight, gcn.6.gcn.bn.bias, gcn.6.gcn.bn.running_mean, gcn.6.gcn.bn.running_var, gcn.6.gcn.conv.weight, gcn.6.gcn.conv.bias, gcn.6.tcn.conv.weight, gcn.6.tcn.conv.bias, gcn.6.tcn.bn.weight, gcn.6.tcn.bn.bias, gcn.6.tcn.bn.running_mean, gcn.6.tcn.bn.running_var, gcn.7.gcn.PA, gcn.7.gcn.A, gcn.7.gcn.bn.weight, gcn.7.gcn.bn.bias, gcn.7.gcn.bn.running_mean, gcn.7.gcn.bn.running_var, gcn.7.gcn.conv.weight, gcn.7.gcn.conv.bias, gcn.7.tcn.conv.weight, gcn.7.tcn.conv.bias, gcn.7.tcn.bn.weight, gcn.7.tcn.bn.bias, gcn.7.tcn.bn.running_mean, gcn.7.tcn.bn.running_var, gcn.7.residual.conv.weight, gcn.7.residual.conv.bias, gcn.7.residual.bn.weight, gcn.7.residual.bn.bias, gcn.7.residual.bn.running_mean, gcn.7.residual.bn.running_var, gcn.8.gcn.PA, gcn.8.gcn.A, gcn.8.gcn.bn.weight, gcn.8.gcn.bn.bias, gcn.8.gcn.bn.running_mean, gcn.8.gcn.bn.running_var, gcn.8.gcn.conv.weight, gcn.8.gcn.conv.bias, gcn.8.tcn.conv.weight, gcn.8.tcn.conv.bias, gcn.8.tcn.bn.weight, gcn.8.tcn.bn.bias, gcn.8.tcn.bn.running_mean, gcn.8.tcn.bn.running_var, gcn.9.gcn.PA, gcn.9.gcn.A, gcn.9.gcn.bn.weight, gcn.9.gcn.bn.bias, gcn.9.gcn.bn.running_mean, gcn.9.gcn.bn.running_var, gcn.9.gcn.conv.weight, gcn.9.gcn.conv.bias, gcn.9.tcn.conv.weight, gcn.9.tcn.conv.bias, gcn.9.tcn.bn.weight, gcn.9.tcn.bn.bias, gcn.9.tcn.bn.running_mean, gcn.9.tcn.bn.running_var

Name of parameter - Initialization information

data_bn.weight - torch.Size([51]): 
The value is the same before and after calling `init_weights` of STGCN  

data_bn.bias - torch.Size([51]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.conv.weight - torch.Size([192, 3, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.0.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.0.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.0.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.conv.weight - torch.Size([192, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.1.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.1.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.1.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.conv.weight - torch.Size([192, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.2.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.2.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.2.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.conv.weight - torch.Size([192, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.gcn.conv.bias - torch.Size([192]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.tcn.conv.weight - torch.Size([64, 64, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.3.tcn.conv.bias - torch.Size([64]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.3.tcn.bn.weight - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.3.tcn.bn.bias - torch.Size([64]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.conv.weight - torch.Size([384, 64, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.gcn.conv.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.tcn.conv.weight - torch.Size([128, 128, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.tcn.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.tcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.tcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.residual.conv.weight - torch.Size([128, 64, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.residual.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.4.residual.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.4.residual.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.conv.weight - torch.Size([384, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.gcn.conv.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.tcn.conv.weight - torch.Size([128, 128, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.5.tcn.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.5.tcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.5.tcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.conv.weight - torch.Size([384, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.gcn.conv.bias - torch.Size([384]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.tcn.conv.weight - torch.Size([128, 128, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.6.tcn.conv.bias - torch.Size([128]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.6.tcn.bn.weight - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.6.tcn.bn.bias - torch.Size([128]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.conv.weight - torch.Size([768, 128, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.gcn.conv.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.tcn.conv.weight - torch.Size([256, 256, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.tcn.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.tcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.tcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.residual.conv.weight - torch.Size([256, 128, 1, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.residual.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.7.residual.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.7.residual.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.conv.weight - torch.Size([768, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.gcn.conv.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.tcn.conv.weight - torch.Size([256, 256, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.8.tcn.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.8.tcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.8.tcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.PA - torch.Size([3, 17, 17]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.conv.weight - torch.Size([768, 256, 1, 1]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.gcn.conv.bias - torch.Size([768]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.tcn.conv.weight - torch.Size([256, 256, 9, 1]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.9.tcn.conv.bias - torch.Size([256]): 
KaimingInit: a=0, mode=fan_out, nonlinearity=relu, distribution =normal, bias=0 

gcn.9.tcn.bn.weight - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  

gcn.9.tcn.bn.bias - torch.Size([256]): 
The value is the same before and after calling `init_weights` of STGCN  
2025/08/08 11:54:52 - mmengine - WARNING - "FileClient" will be deprecated in future. Please use io functions in https://mmengine.readthedocs.io/en/latest/api/fileio.html#file-io
2025/08/08 11:54:52 - mmengine - WARNING - "HardDiskBackend" is the alias of "LocalBackend" and the former will be deprecated in future.
2025/08/08 11:54:52 - mmengine - INFO - Checkpoints will be saved to /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808/tools/work_dirs/pose_rgb_fusion.
