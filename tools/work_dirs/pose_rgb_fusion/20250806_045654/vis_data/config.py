Test_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl'
TrainVal_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl'
dataset_type = 'PoseRgbDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=20, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
find_unused_parameters = True
launcher = 'pytorch'
load_from = None
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
model = dict(
    cls_head=dict(
        in_channels=512,
        loss_cls=dict(
            class_weight=[
                1.0,
                1.0,
                1.0,
            ],
            loss_weight=1.0,
            type='CrossEntropyLoss'),
        num_classes=3,
        type='TwoFusionHead'),
    fusion_neck=dict(
        dropout=0.5,
        fusion_dim=512,
        fusion_type='cross_modal',
        img_feat_dim=320,
        pose_feat_dim=256,
        type='MultiModalFusionNeck'),
    image_backbone=dict(
        frozen_stages=-1,
        init_cfg=dict(
            checkpoint=
            '../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth',
            type='Pretrained'),
        is_shift=True,
        norm_eval=False,
        num_segments=3,
        out_indices=(6, ),
        pretrained2d=False,
        shift_div=8,
        type='MobileNetV2TSM'),
    pose_backbone=dict(
        graph_cfg=dict(layout='coco', mode='stgcn_spatial'),
        in_channels=3,
        init_cfg=dict(
            checkpoint=
            '../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth',
            type='Pretrained'),
        type='STGCN'),
    test_cfg=dict(average_clips='prob'),
    train_cfg=None,
    type='MultiModalRecognizer')
num_classes = 3
optim_wrapper = dict(
    clip_grad=dict(max_norm=20, norm_type=2),
    optimizer=dict(
        betas=(
            0.9,
            0.999,
        ), lr=0.001, type='AdamW', weight_decay=0.0001),
    paramwise_cfg=dict(
        custom_keys=dict(
            cls_head=dict(lr_mult=1.0),
            fusion_neck=dict(lr_mult=1.0),
            image_backbone=dict(lr_mult=0.1),
            pose_backbone=dict(lr_mult=1.0))),
    type='OptimWrapper')
param_scheduler = [
    dict(begin=0, by_epoch=True, end=5, start_factor=0.01, type='LinearLR'),
    dict(
        T_max=95,
        begin=5,
        by_epoch=True,
        end=100,
        eta_min=1e-06,
        type='CosineAnnealingLR'),
]
randomness = dict(deterministic=False, diff_rank_seed=False, seed=None)
resume = False
split_label = dict({
    0: [
        1,
        2,
    ],
    1: [
        0,
    ]
})
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=64,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(input_format='NCHW', type='FormatShape'),
            dict(
                collect_keys=(
                    'keypoint',
                    'imgs',
                ),
                pred_txt=
                '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets',
                split_label=dict({
                    0: [
                        1,
                        2,
                    ],
                    1: [
                        0,
                    ]
                }),
                type='PackActionInputs_Test'),
        ],
        split=None,
        test_mode=True,
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = [
    dict(type='ClassifyReport'),
]
test_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(input_format='NCHW', type='FormatShape'),
    dict(
        collect_keys=(
            'keypoint',
            'imgs',
        ),
        pred_txt=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets',
        split_label=dict({
            0: [
                1,
                2,
            ],
            1: [
                0,
            ]
        }),
        type='PackActionInputs_Test'),
]
train_cfg = dict(
    max_epochs=100, type='EpochBasedTrainLoop', val_begin=1, val_interval=1)
train_dataloader = dict(
    batch_size=128,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, seed=255, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                load_Resized_img=True,
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(type='ColorJitter'),
            dict(flip_ratio=0.5, type='Flip'),
            dict(input_format='NCHW', type='FormatShape'),
            dict(collect_keys=(
                'keypoint',
                'imgs',
            ), type='PackActionInputs'),
        ],
        split='train',
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, seed=255, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        load_Resized_img=True,
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(type='ColorJitter'),
    dict(flip_ratio=0.5, type='Flip'),
    dict(input_format='NCHW', type='FormatShape'),
    dict(collect_keys=(
        'keypoint',
        'imgs',
    ), type='PackActionInputs'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=64,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
            dict(dataset='coco', feats=[
                'b',
            ], type='GenSkeFeat'),
            dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
            dict(type='SimplifiedFormatGCNInput'),
            dict(
                load_Resized_img=True,
                pad_val=(
                    128,
                    128,
                    128,
                ),
                scale=(
                    224,
                    224,
                ),
                type='SimplyResize',
                use_padding=True),
            dict(input_format='NCHW', type='FormatShape'),
            dict(collect_keys=(
                'keypoint',
                'imgs',
            ), type='PackActionInputs'),
        ],
        split='val',
        test_mode=True,
        type='PoseRgbDataset'),
    num_workers=8,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = [
    dict(type='AccMetric'),
]
val_pipeline = [
    dict(left_idx=11, right_idx=12, type='fusionPreNormalize2D'),
    dict(dataset='coco', feats=[
        'b',
    ], type='GenSkeFeat'),
    dict(clip_len=35, test_mode=True, type='UniformSampleFrames'),
    dict(type='SimplifiedFormatGCNInput'),
    dict(
        load_Resized_img=True,
        pad_val=(
            128,
            128,
            128,
        ),
        scale=(
            224,
            224,
        ),
        type='SimplyResize',
        use_padding=True),
    dict(input_format='NCHW', type='FormatShape'),
    dict(collect_keys=(
        'keypoint',
        'imgs',
    ), type='PackActionInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    type='ActionVisualizer', vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = './work_dirs/pose_rgb_fusion'
