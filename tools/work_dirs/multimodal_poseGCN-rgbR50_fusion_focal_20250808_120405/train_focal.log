2025/08/08 12:04:05 - mmengine - INFO - ============================================================
2025/08/08 12:04:05 - mmengine - INFO - 🚀 开始训练多模态识别模型 - Focal Loss优化版本
2025/08/08 12:04:05 - mmengine - INFO - 📁 配置文件: ../configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py
2025/08/08 12:04:05 - mmengine - INFO - 📂 工作目录: ./work_dirs/multimodal_poseGCN-rgbR50_fusion_focal_20250808_120405
2025/08/08 12:04:05 - mmengine - INFO - 🎯 损失函数类型: FocalLossWithSmoothing
2025/08/08 12:04:05 - mmengine - INFO -    ├─ Alpha权重: auto
2025/08/08 12:04:05 - mmengine - INFO -    ├─ Gamma参数: 2.0
2025/08/08 12:04:05 - mmengine - INFO -    └─ Label Smoothing: 0.1
2025/08/08 12:04:05 - mmengine - INFO - 🔧 自定义Hook数量: 2
2025/08/08 12:04:05 - mmengine - INFO -    ├─ Hook 1: ClassWeightHook (权重更新间隔: 10轮)
2025/08/08 12:04:05 - mmengine - INFO -    ├─ Hook 2: HardSampleHook (困难样本阈值: 0.7)
2025/08/08 12:04:05 - mmengine - INFO - 📊 训练轮数: 100
2025/08/08 12:04:05 - mmengine - INFO - 📦 批次大小: 4
2025/08/08 12:04:05 - mmengine - INFO - ============================================================
2025/08/08 12:04:07 - mmengine - INFO - 🏃 开始训练...
2025/08/08 12:04:08 - mmengine - ERROR - /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808/tools/train_focal.py - main - 214 - ❌ 训练过程中出现错误: Expected input type to be list, but got <class 'dict'>
